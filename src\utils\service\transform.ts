import qs from 'qs';
import FormData from 'form-data';
import { isArray, isFile } from '../common';

/**
 * 请求数据的转换
 * @param requestData - 请求数据
 * @param contentType - 请求头的Content-Type
 */
export async function transformRequestData(requestData: any, contentType?: UnionKey.ContentType) {
  // application/json类型不处理
  let data = requestData;
  // form类型转换
  if (contentType === 'application/x-www-form-urlencoded') {
    data = qs.stringify(requestData);
  }
  // form-data类型转换
  if (contentType === 'multipart/form-data') {
    data = await handleFormData(requestData);
  }

  return data;
}

async function handleFormData(data: Record<string, any>) {
  const formData = new FormData();
  const entries = Object.entries(data);

  entries.forEach(async ([key, value]) => {
    const isFileType = isFile(value) || (isArray(value) && value.length && isFile(value[0]));

    if (isFileType) {
      await transformFile(formData, key, value);
    } else {
      formData.append(key, value);
    }
  });

  return formData;
}

/**
 * 接口为上传文件的类型时数据转换
 * @param key - 文件的属性名
 * @param file - 单文件或多文件
 */
async function transformFile(formData: FormData, key: string, file: File[] | File) {
  if (isArray(file)) {
    // 多文件
    await Promise.all(
      (file as File[]).map(item => {
        formData.append(key, item);
        return true;
      })
    );
  } else {
    // 单文件
    formData.append(key, file);
  }
}

/**
 * 下载
 * @param  {String} fileUrl 目标文件地址
 * @param  {String} filename 想要保存的文件名称
 */
export function renameDownload(fileUrl: any, fileName: any) {
  let link = document.createElement("a");
  //这里属性涉及到跨域问题后，设置无效
  //link.download = fileName;
  link.style.display = "none";
  //response-content-type=application/octet-stream,这里是为了防止图片，pdf，txt文件直接被浏览器打开
  //response-content-disposition的设置，解决跨域文件名重命名无效问题，encodeURIComponent防止文件名带特殊字符（例如&等）导致下载文件名出问题
  link.href =
    fileUrl +
    "?response-content-type=application/octet-stream&response-content-disposition=filename=" +
    `${encodeURIComponent(fileName)}`;
  document.body.appendChild(link);
  link.click();
  URL.revokeObjectURL(link.href);
  // document.body.removeChild(link, fileName);
  document.body.removeChild(link);
}
