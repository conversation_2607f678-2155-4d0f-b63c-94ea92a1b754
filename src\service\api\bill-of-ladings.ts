import { request } from "../request";



export const fetchBillOfLadingPage = async (params: any) => {
	const data = await request.post("/bill_of_lading/page", params);
	return data;
};

export const addNewBillOfLading = async (params: any) => {
	const data = await request.post("/bill_of_lading/add", params);
	return data;
};

export const updateBillOfLading= async (params: any) => {
	const data = await request.post("/bill_of_lading/update", params);
	return data;
};

export const deleteBillOfLading= async (params: any) => {
	const data = await request.post("/bill_of_lading/deleteBatchIds", params);
	return data;
};

export const refreshBillOfLading= async (params: any) => {
	const data = await request.post("/bill_of_lading/refresh", params);
	return data;
};

