<template>
	<div>
		<n-card :bordered="false" class="rounded-16px shadow-sm">
			<n-form ref="formRef" :model="searchCond" label-placement="top" label-width="auto" size="medium"
				:show-label="true">
				<n-grid :cols="24" :x-gap="24">
					<n-form-item-gi :span="8" path="blNo" label="B/L No.">
						<n-input v-model:value="searchCond.blNo" placeholder="B/L No." clearable />
					</n-form-item-gi>

					<n-form-item-gi :span="8">
						<n-space>
							<n-button type="primary" @click="pagination.page = 1; getTableData()">
								Search List
							</n-button>
							<n-button type="default" @click="resetTable">
								Reset
							</n-button>
						</n-space>
					</n-form-item-gi>
				</n-grid>
			</n-form>
			<n-space class="pb-12px" justify="space-between">
				<n-space>
					<n-button type="primary" style="margin-left: 16px;" @click="batchAddLading">
						Add B/L
					</n-button>
					<n-button type="primary" style="margin-left: 16px;" @click="batchDelete"
						:disabled="selectRowskeys.length < 2">
						Batch Delete
					</n-button>
				</n-space>
			</n-space>
		</n-card>
		<n-space :vertical="true" :size="16">

			<n-card :bordered="false" class="h-full rounded-16px shadow-sm">
				<n-data-table remote v-model:checked-row-keys="selectRowskeys" :columns="columns" :data="tableData"
					:single-line="false" :loading="loading" :scroll-x="1500" :row-key="rowKey"  
					@update:checked-row-keys="handleCheck" :pagination="pagination" 
					@update:page="getTableData"
					@update-page-size="getTableData" striped />
			</n-card>
			

			<n-modal v-model:show="showUploadModal" :mask-closable="false" class="custom-card" preset="card"
				:style="bodyStyle2" title="Add B/L" size="huge" :bordered="false" :segmented="segmented"
				@after-leave="leaveUpload">
				<n-form ref="formRefAuto" :model="batchUploadModel" :rules="rulesUpload" size="medium" label-align="left"
					label-placement="left" label-width="auto">
					<n-grid :cols="24" :x-gap="24">
						<n-form-item-gi :span="20" label="File Upload:" path="fileName">
							<!-- 点击上传 -->
							<n-upload class="upload_icon" accept=".pdf" v-show="batchUploadModel.file == null"
								:custom-request="customRequest" @before-upload="beforeUpload"
								:show-file-list="false">
								<n-button size="large"> Upload</n-button>
							</n-upload>
							<span v-if="batchUploadModel.file"
								class="lh-34px mr-6px">{{ batchUploadModel.file?.name }}</span>
							<n-upload v-if="batchUploadModel.file" class="upload_icon w-40px lh-34px" accept=".xls,.xlsx"
								:custom-request="(v: any) => { customRequest({ ...v, flag: 1 }) }" @before-upload="beforeUpload"
								:show-file-list="false">
								<span class="text-blue">重选</span>
							</n-upload>
						</n-form-item-gi>
					</n-grid>
				</n-form>
				<template #footer>
					<div style="display: flex; justify-content: flex-end;">
						<n-button type="primary" style="margin-right: 15px;" @click="addNewBillOfNumber"
							:disabled="batchUploadModel.file == null" :loading="btnLoading">Submit</n-button>
						<n-button @click='leaveUpload'>Back</n-button>
					</div>
				</template>
			</n-modal>

			<n-modal v-model:show="showEditModal" :mask-closable="false" class="custom-card" preset="card"
				:style="bodyStyle2" title="Add B/L" size="huge" :bordered="false" :segmented="segmented"
				@after-leave="leaveUpload">
				<n-form ref="formRefAuto" :model="ladingEditModel" :rules="rulesUpload" size="medium" label-align="left"
					label-placement="left" label-width="auto">
					<n-grid :cols="24" :x-gap="24">
						<n-form-item-gi :span="20" label="id">
							<n-input v-model:value="ladingEditModel.id" :disabled="true" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Carrier">
							<n-select v-model:value="ladingEditModel.carrier" :options="carrierOptions" placeholder="Please choose" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="B/L No.">
							<n-input v-model:value="ladingEditModel.billOfLadingNumber" placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="WAY BILL No.">
							<n-input v-model:value="ladingEditModel.waybillNumber"  placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Container No.">
							<n-input
								v-for="(item, index) in ladingEditModel.containerNumbers"
								:key="index"
								v-model:value="ladingEditModel.containerNumbers[index]"
								placeholder="Please input"
								style="margin-bottom: 10px"
								/>
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Voy No.">
							<n-input v-model:value="ladingEditModel.voyageNumber" placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Vessel">
							<n-input v-model:value="ladingEditModel.vessel"  placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Port Of Loading">
							<n-input v-model:value="ladingEditModel.loadingPort"  placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Port Of Discharge">
							<n-input v-model:value="ladingEditModel.dischargePort" placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Gross Weight">
							<n-input v-model:value="ladingEditModel.totalGrossWeight" placeholder="Please input" />
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="Tare">
							<n-input v-model:value="ladingEditModel.totalTare" placeholder="Please input" />
						</n-form-item-gi>						
					</n-grid>
				</n-form>

				<template #footer>
					<div style="display: flex; justify-content: flex-end;">
						<n-button type="primary" style="margin-right: 15px;" @click="editLadingSubmit"
							:loading="btnLoading">Submit</n-button>
						<n-button @click='leaveEdit'>Back</n-button>
					</div>
				</template>
			</n-modal>

		</n-space>
	</div>
</template>

<script setup lang="tsx">
import { reactive, ref, onMounted } from 'vue';
import type { Ref } from 'vue';
import { uniq, remove } from 'lodash-es';
import { NButton, FormInst } from 'naive-ui';
import type { PaginationProps, UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { fetchBillOfLadingPage, addNewBillOfLading, updateBillOfLading, deleteBillOfLading, fetchUpload, refreshBillOfLading  } from '@/service';
import { useBoolean, useLoading } from '@/hooks';
import { createRequiredFormRule } from '@/utils';
import { useIconRender } from '@/composables';
import { getTrackingUrl } from '@/constants';

const { iconRender } = useIconRender();

const { loading, startLoading, endLoading } = useLoading(true);
const { loading: btnLoading, startLoading: btnStartLoading, endLoading: btnEndLoading } = useLoading(false);

const formRefAuto = ref<HTMLElement & FormInst>();

const showUploadModal = ref(false)
const showEditModal = ref(false)


const bodyStyle2 = {
	width: "650px",
	minWidth: 0,
}

const segmented = reactive(
	{
		content: 'soft',
		footer: 'soft'
	}
)

interface File {
	name: string,
	url: string,
}

const carrierOptions = [
    {
		label: 'CMA',
		value: 'CMA',
	},	
    {
		label: 'COSCO',
		value: 'COSCO',
	},
	{
		label: 'YML',
		value: 'YML',
	},
	{
		label:'HMM',
		value:'HMM',
	},
	{
		label:'TSL',
		value:'TSL',
	},
	{
		label:'ONE',
		value:'ONE',
	},
	{
		label:'OOCL',
		value:'OOCL',
	},
	{
		label: 'KMTC',
		value: 'KMTC',
	},
	{
		label: 'MSC',
		value: 'MSC',
	},
	{
		label: 'MSK',
		value: 'MSK',
	},
	{
		label: 'EMC',
		value: 'EMC',
	},
	{
		label: 'IAL',
		value: 'IAL',
	},
	{
		label: 'PIL',
		value: 'PIL',
	},
	{
		label: 'WHL',
		value: 'WHL',
	},
]

const batchUploadModel = reactive({
	carrier: null as string | null,
	file: null as File | null,
});

const rulesUpload = {
	fileName: createRequiredFormRule('请上传文件'),
	taskName: createRequiredFormRule('请输入任务名称'),
}

const ladingEditModel = reactive({	
	id: null as number | null,
	carrier: null as string | null,
	billOfLadingNumber: null as string | null,
	waybillNumber: null as string | null,
	containerNumbers: [] as string[] | [],
	voyageNumber: null as string | null,
	vessel: null as string | null,
	loadingPort: null as string | null,
	dischargePort: null as string | null,
	totalGrossWeight: null as string | null,
	totalTare: null as string | null,
})

const customRequest = async ({
	file,
	action,
	onFinish,
	onError,
	flag
}: UploadCustomRequestOptions & { flag?: number }) => {
	try {
		if (flag == 1) {
			batchUploadModel.file = null
		}
		const { code, data } = await fetchUpload({ file: file.file })
		if (code == 200) {
			batchUploadModel.file = data as File
			onFinish()
		} else {
			onError()
		}
	} catch (e) {
		console.error(e)
		onError()
	}
}

const beforeUpload = async ({ file, fileList }: { file: UploadFileInfo, fileList: Array<UploadFileInfo>, event?: Event }) => {
	if ((file.file?.size || 0) > 2 * 1024 * 1024) {
		window.$message?.error('上传失败，文件大小超出限制，请重新上传!');
		return false;
	}
	return true
}

const selectRowskeys = ref<Array<string | number>>([])
const selectRowsId = ref<string[]>([])
function rowKey(row: any) {
	return row.id
}

function handleCheck(rowKeys: Array<string | number>, rows: any[], meta: { row?: any, action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }) {
	const tempId = meta.row?.id
	switch (meta.action) {
		case 'check':
			if (tempId !== undefined && tempId !== null) {
				selectRowsId.value = uniq([...
					selectRowsId.value,
					String(tempId)
				])
			}
			break;
		case 'uncheck':
			remove(selectRowsId.value, v => v === String(tempId))
			break;
		case 'checkAll':
			selectRowsId.value = uniq(rowKeys.map(k => String(k)))
			break;
		case 'uncheckAll':
		selectRowsId.value = []
			break;
	}
	selectRowskeys.value = rowKeys as any
}

function batchAddLading() {
	showUploadModal.value = true
}

function editRow(item: any) {
	showEditModal.value = true
	ladingEditModel.id = item.id
	ladingEditModel.carrier = item.carrier
	ladingEditModel.billOfLadingNumber = item.billOfLadingNumber
	ladingEditModel.waybillNumber = item.waybillNumber
	ladingEditModel.voyageNumber = item.voyageNumber
	ladingEditModel.vessel = item.vessel
	ladingEditModel.loadingPort = item.loadingPort
	ladingEditModel.dischargePort = item.dischargePort
	ladingEditModel.containerNumbers = item.containerNumbers || ['']
	ladingEditModel.totalGrossWeight = item.totalGrossWeight
	ladingEditModel.totalTare = item.totalTare
	
}

const searchCond = reactive({
	blNo: ""
});

const columns = ref([
	{
		type: 'selection',
		align: 'center',
		fixed: 'left',
	},
	{
	  type: 'expand',
	  expandable: () => true,
	  renderExpand: (rowData: any) => (
	      <div class="bill-of-lading-expand">
	        <div class="expand-content">
	          <div class="info-grid">
	            <div class="info-item">
	              <span class="info-label">航线</span>
	              <span class="info-value">{rowData.voyageNumber || '-'}</span>
	            </div>
	            <div class="info-item">
	              <span class="info-label">船名</span>
	              <span class="info-value">{rowData.vessel || '-'}</span>
	            </div>
	            <div class="info-item">
	              <span class="info-label">装货港口</span>
	              <span class="info-value">{rowData.loadingPort || '-'}</span>
	            </div>
	            <div class="info-item">
	              <span class="info-label">卸货港口</span>
	              <span class="info-value">{rowData.dischargePort || '-'}</span>
	            </div>
	            <div class="info-item">
	              <span class="info-label">毛重 (KGS)</span>
	              <span class="info-value">{rowData.totalGrossWeight || '-'}</span>
	            </div>
	            <div class="info-item">
	              <span class="info-label">皮重 (KGS)</span>
	              <span class="info-value">{rowData.totalTare || '-'}</span>
	            </div>
	          </div>
	        </div>
	      </div>
	  ),
	},
	{
		key: 'index',
		title: '#',
		width: '50px',
		align: 'center',
		fixed: 'left',
	},
	{
		key: 'carrier',
		title: 'Carrier',
		width: '180px',
		align: 'center',
		fixed: 'left',
	},
	{
		key: 'billOfLadingNumber',
		title: 'B/L No.',
		minWidth: '120px',
		align: 'center',
	},
	{
		key: 'waybillNumber',
		title: 'WAY BILL No.',
		width: '180px',
		align: 'center'
	},
	{
		key: 'containerNumbers',
		title: 'Container No.',
		width: '220px',
		align: 'center',
		render: (row: { containerNumbers: string[]}) => {
			return (
				<div style="display:flex;justify-content:space-evenly">
					{row.containerNumbers?.join("\n")}
				</div>
			)
		}
	},
	{
		key: 'etaDate',
		title: 'ETA',
		minWidth: '200px',
		align: 'center',
		render: (row: { carrier: string; containerNumbers: string[]; status: number; etaDate: any; }) => {
			const renderEtaStatus = () => {
				const statusConfig: Record<number, {
					color: string;
					icon: string;
					text: string;
					showText: boolean;
				}> = {
					1: {
						color: '#4285F4',
						icon: 'fluent:scan-text-28-regular',
						text: 'Pdf Parsing',
						showText: true
					},
					2: {
						color: 'rgb(164,0,0)',
						icon: 'mi:circle-error',
						text: 'Parsing Failed',
						showText: true
					},
					3: {
						color: '#4285F4',
						icon: 'qlementine-icons:clock-16',
						text: 'ETA Querying',
						showText: true
					},
					4: {
						color: 'green',
						icon: 'qlementine-icons:success-16',
						text: '',
						showText: false
					},
					5: {
						color: 'rgb(164,0,0)',
						icon: 'mi:circle-error',
						text: '',
						showText: false
					},
					6: {
						color: 'orange',
						icon: 'qlementine-icons:warning-16',
						text: 'To Be Confirmed',
						showText: true
					},
					7: {
						color: 'orange',
						icon: 'qlementine-icons:warning-16',
						text: 'To Be Adviced',
						showText: true
					}
				};

				const config = statusConfig[row.status];
				if (!config) return null;

				const iconButton = (
					<NButton
						style={`color:${config.color};font-size:24px;${config.showText ? 'margin-right:10px' : ''}`}
						size={'medium'}
						text
					>
						{iconRender({ icon: config.icon })}
					</NButton>
				);

				if (config.showText) {
					return (
						<div style="display:flex;justify-content:center">
							{iconButton}
							<p style={`color:${config.color}`}>{config.text}</p>
						</div>
					);
				}

				return iconButton;
			};

			let tracking_url = getTrackingUrl(row.carrier, row.containerNumbers[0]);

			return (
				<div style="display:flex;justify-content:center">
					{renderEtaStatus()}
					{row.status == 4 && row.etaDate && <p style='margin-left:10px'>{row.etaDate}</p>}
					{<a href="${tracking_url}" target="_blank" rel="noopener">手动查询</a>}
				</div>
			);
		}
	},

	{
		key: 'creator',
		title: 'Creator',
		width: '90px',
		align: 'center',
	},

	{
		key: 'createTime',
		title: 'Create Date',
		width: '180px',
		align: 'center',
		fixed: 'right',
	},
	{
		key: 'updateBy',
		title: 'Last Update',
		// width: '120px',
		align: 'center',
		fixed: 'right',
	},

	{
		key: 'actions',
		title: 'Action',
		width: '220px',
		align: 'center',
		fixed: 'right',
		rowSpan: (row: any) => { return row.rowSpan },
		render: (row: any) => {
			return (
				<div style="display:flex;justify-content:space-evenly">
					<NButton style='color:rgb(0,157,255);font-size:24px' size={'medium'} text onClick={() => refreshStatus(row)}>
						{iconRender({ icon: 'streamline:magnifying-glass' })}
					</NButton>
					<NButton onClick={() => editRow(row)} style='color:rgb(0,157,255);font-size:24px' size={'medium'} text >
						{iconRender({ icon: 'material-symbols:contract-edit-outline' })}
					</NButton>
					<NButton style='color:red;font-size:24px' size={'medium'} text onClick={() => batchDelete(row)}  >
						{iconRender({ icon: 'ri:delete-bin-6-line' })}
					</NButton>
				</div>
			)
		}
	},
]);
const tableTotal = ref<number>(0)
const tableData = ref([]);
async function getTableData() {
	startLoading();
	selectRowskeys.value = [];//清空批量选择的行
	selectRowsId.value = []; //清空批量选择的行
	const params = {
		size: pagination.pageSize || 10,
		current: pagination.page,
		...searchCond
	}
	const resp: any = await fetchBillOfLadingPage(params);
	endLoading();
	if (resp?.code == 200) {
		const data = resp.data || {};
		tableTotal.value = data?.total || 0;
		pagination.page = data?.current || 1;
		pagination.pageCount = Math.ceil((data?.total || 0) / (pagination?.pageSize || 10));
		tableData.value = []
		const list = Array.isArray(data?.list) ? data.list : []
		list.forEach((item: any, index: number) => {
			item.index = index + 1

		});
		tableData.value = list;
	}
}

function resetTable() {
	searchCond.blNo = ''
	pagination.page = 1

	selectRowsId.value = []
	selectRowskeys.value = []
	getTableData();
}

const pagination: PaginationProps = reactive({
	page: 1,
	pageSize: 10,
	pageCount: 0,
	showSizePicker: true,
	showQuickJumper: true,
	pageSizes: [10, 20, 50, 100],
	prefix: () => <p>Total:{tableTotal.value} </p>,
	onChange: (page: number) => {
		pagination.page = page;
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.pageSize = pageSize;
		pagination.page = 1;
	},
	goto: () => <div>to</div>
});

async function init() {
	getTableData();
}


function leaveUpload() {
	batchUploadModel.carrier = '';
	batchUploadModel.file = null;
	showUploadModal.value = false
}


async function addNewBillOfNumber() {
	const params = {
		carrier: batchUploadModel.carrier,
		docInfo: batchUploadModel.file
	}
	btnStartLoading()
	const { code, message } = await addNewBillOfLading(params)
	btnEndLoading()
	if (code == 200) {
		leaveUpload()
	} else {
		window.$message?.error('error:' + message);
	}
}

async function editLadingSubmit() {
	const params = {
		id: ladingEditModel.id,
		carrier: ladingEditModel.carrier,
		billOfLadingNumber: ladingEditModel.billOfLadingNumber,
		waybillNumber: ladingEditModel.waybillNumber,
		voyageNumber: ladingEditModel.voyageNumber,
		vessel: ladingEditModel.vessel,
		loadingPort: ladingEditModel.loadingPort,
		dischargePort: ladingEditModel.dischargePort,
		containerNumbers: ladingEditModel.containerNumbers,
		totalGrossWeight: ladingEditModel.totalGrossWeight,
		totalTare: ladingEditModel.totalTare,
	}
	btnStartLoading()
	const { code, message } = await updateBillOfLading(params)
	btnEndLoading()
	if (code == 200) {
		leaveEdit()
	} else {
		window.$message?.error('error:' + message);
	}
}

function leaveEdit() {
	showEditModal.value = false
}

const batchDelete = async (row: any) => {
	let params
	if (row.id) {
		params = [row.id]
	} else {
		params = selectRowskeys.value
	}
	const { code, data } = await deleteBillOfLading(params);
	if (code == 200) {
		window.$message?.success('success');
		getTableData()

	} else {
		window.$message?.error('error');
	}
}

const refreshStatus = async (row: any) => {
	let params= {id: row.id}
	const { code } = await refreshBillOfLading(params);
	if (code == 200) {
		window.$message?.success('success');
		getTableData()

	} else {
		window.$message?.error('error');
	}
}

onMounted(() => {
	init()
})
</script>

<style scoped>
.spucode {
	word-break: break-all;
	word-wrap: normal
}

:deep(.bill-of-lading-expand) {
  /* padding: 16px; */
  background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  /* margin: 8px 0; */
}


:deep(.expand-content) {
  background: white;
  border-radius: 6px;
  /* padding: 16px; */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.info-grid) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2px;
}

:deep(.info-item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

:deep(.info-item:hover) {
  background-color: #f8f9fa;
}

:deep(.info-label) {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
  min-width: 100px;
}

:deep(.info-value) {
  color: #2c3e50;
  font-size: 14px;
  text-align: right;
  flex: 1;
  margin-left: 16px;
  word-break: break-word;
}

@media (max-width: 768px) {
  :deep(.info-grid) {
    grid-template-columns: 1fr;
  }
  
  :deep(.info-item) {
    flex-direction: column;
    align-items: flex-start;
  }
  
  :deep(.info-value) {
    text-align: left;
    margin-left: 0;
    margin-top: 4px;
  }
}
</style>











