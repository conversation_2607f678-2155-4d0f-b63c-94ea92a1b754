{"name": "smart tracking", "version": "0.10.2", "description": "A fresh and elegant mis template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Fyz Mis", "email": "<EMAIL>"}, "license": "MIT", "type": "module", "scripts": {"dev": "cross-env VITE_SERVICE_ENV=dev vite --mode dev", "dev:test": "cross-env VITE_SERVICE_ENV=test vite --mode test", "dev:uat": "cross-env VITE_SERVICE_ENV=uat vite --mode uat", "dev:prod": "cross-env VITE_SERVICE_ENV=prod vite --mode production", "build": "cross-env VITE_SERVICE_ENV=prod vite build", "build:test": "cross-env VITE_SERVICE_ENV=test VITE_HTTP_PROXY=Y vite build --mode test", "build:uat": "cross-env VITE_SERVICE_ENV=uat VITE_HTTP_PROXY=N vite build --mode uat", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint . --fix", "tsx": "tsx", "logo": "tsx ./scripts/logo.ts"}, "dependencies": {"@antv/data-set": "0.11.8", "@antv/g2": "4.2.10", "@better-scroll/core": "2.5.1", "@soybeanjs/vue-materials": "0.2.0", "@types/big.js": "^6.2.2", "@vicons/antd": "^0.12.0", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vueuse/core": "10.1.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.4.0", "big.js": "^6.2.1", "clipboard": "2.0.11", "colord": "2.9.3", "crypto-js": "4.1.1", "dayjs": "1.11.8", "decimal.js": "^10.4.3", "echarts": "5.4.2", "file-saver": "^2.0.5", "form-data": "4.0.0", "fyz-mis": "file:", "lodash-es": "4.17.21", "naive-ui": "^2.38.1", "number-precision": "^1.6.0", "pinia": "2.1.3", "print-js": "1.6.0", "qs": "6.11.2", "swiper": "9.3.2", "ua-parser-js": "1.0.35", "vditor": "3.9.3", "vue": "3.3.4", "vue-router": "4.2.2", "vuedraggable": "4.1.0", "xgplayer": "3.0.4", "xlsx": "^0.18.5"}, "devDependencies": {"@amap/amap-jsapi-types": "0.0.13", "@iconify/json": "2.2.74", "@iconify/vue": "4.1.1", "@soybeanjs/vite-plugin-vue-page-route": "0.0.5", "@types/bmapgl": "0.0.7", "@types/crypto-js": "4.1.1", "@types/file-saver": "^2.0.7", "@types/node": "20.2.5", "@types/qs": "6.9.7", "@types/ua-parser-js": "0.7.36", "@unocss/preset-uno": "0.53.0", "@unocss/transformer-directives": "0.53.0", "@unocss/vite": "0.53.0", "@vicons/carbon": "^0.12.0", "@vitejs/plugin-vue": "4.2.3", "@vitejs/plugin-vue-jsx": "3.0.1", "cross-env": "7.0.3", "eslint": "8.42.0", "eslint-config-soybeanjs": "0.4.8", "mockjs": "1.1.0", "rollup-plugin-visualizer": "5.9.0", "sass": "1.62.1", "simple-git-hooks": "2.8.1", "tsx": "3.12.7", "typescript": "5.1.3", "unplugin-icons": "0.16.2", "unplugin-vue-components": "0.25.1", "vite": "4.3.9", "vite-plugin-compression": "0.5.1", "vite-plugin-mock": "2.9.8", "vite-plugin-progress": "0.0.7", "vite-plugin-pwa": "0.16.3", "vite-plugin-svg-icons": "2.0.1", "vue-tsc": "1.6.5"}, "npm": {"overrides": {"jiti": "1.21.0"}}, "yarn": {"overrides": {"jiti": "1.21.0"}}}