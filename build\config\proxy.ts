import type { ProxyOptions } from 'vite';

/**
 * 设置网络代理
 * @param isOpenProxy - 是否开启代理
 * @param envConfig - env环境配置
 */
export function createViteProxy(isOpenProxy: boolean, envConfig: ServiceEnvConfigWithProxyPattern) {
  if (!isOpenProxy) return undefined;

  const proxy: Record<string, string | ProxyOptions> = {
		[envConfig.proxyPattern]: {
			target: envConfig.url,
			changeOrigin: true,
			// rewrite: (path: string) => {
			// 	const url = path.replace(
			// 		new RegExp(`^\\${envConfig.proxyPattern}`),
			// 		`${envConfig.proxyPattern}`
			// 	);
			// 	console.log(path, envConfig.proxyPattern, url);
			// 	return url;
			// },
		},
		[envConfig.casPattern]: {
			target: envConfig.casUrl,
			changeOrigin: true,
			// rewrite: (path: string) => {
			// 	const url = path.replace(
			// 		new RegExp(`^\\${envConfig.casPattern}`),
			// 		`${envConfig.casPattern}`
			// 	);
			// 	console.log(path, envConfig.casPattern, url);
			// 	return url;
			// },
		},
	};
	console.log('proxy',proxy)
  return proxy;
}

