/** 用户相关模块 */
declare namespace Auth {
	/**
	 * 用户角色类型(前端静态路由用角色类型进行路由权限的控制)
	 * - super: 超级管理员(该权限具有所有路由数据)
	 * - admin: 管理员
	 * - user: 用户
	 */
	type RoleType = "super" | "admin" | "user";

	/** 用户信息 */
	interface UserInfo {
		/** 用户id */
		userId: string;
		/** 用户名 */
		userName: string;
		/** 用户角色类型 */
		userRole: RoleType;
	}
}

declare namespace moneyType {
	interface mType extends ApiMoneyType.moneyType {
		/** 货币代表的值*/
		key: string;
	}
	type moneyTypes = NonNullable<mType["moneyType"]>;
}
/** 企业类别*/
declare namespace businessCategoryType {
	interface categoryType {
		companyCategory: 1 | 2 | 3 | "1" | "2" | "3" | null;
	}
}
declare namespace businessCategory {
	interface mType extends businessCategoryType.categoryType {
		/** 企业类别代表的值*/
		key: string;
	}
	type businessCategoryTypes = NonNullable<mType["companyCategory"]>;
}
/** 企业分组*/
declare namespace businessGroupType {
	interface groupTypes {
		companyGroup:
			| "1"
			| "2"
			| "3"
			| "4"
			| "5"
			| "6"
			| "7"
			| "8"
			| 1
			| 2
			| 3
			| 4
			| 5
			| 6
			| 7
			| 8
			| null;
	}
}
declare namespace businessGroup {
	interface mType extends businessGroupType.groupTypes {
		/** 企业分组代表的值*/
		key: string;
	}
	type businesssGroupTypes = NonNullable<mType["companyGroup"]>;
}
/** 企业类型*/
declare namespace businessTypes {
	interface businessTypes {
		companyType: "1" | "2" | "3" | 1 | 2 | 3 | null;
	}
}
declare namespace businessType {
	interface mType extends businessTypes.businessTypes {
		/** 企业类型代表的值*/
		key: string;
	}
	type businessType = NonNullable<mType["companyType"]>;
}
/** 客商等级*/
declare namespace customerRanking {
	interface customerRank {
		companyLevel:
			| "1"
			| "2"
			| "3"
			| "4"
			| "5"
			| "6"
			| 1
			| 2
			| 3
			| 4
			| 5
			| 6
			| null;
	}
}
declare namespace customerRank {
	interface mType extends customerRanking.customerRank {
		/** 客商等级代表的值*/
		key: string;
	}
	type customerRanks = NonNullable<mType["companyLevel"]>;
}
declare namespace UserManagement {
	interface User extends ApiUserManagement.User {
		/** 序号 */
		index: number;
		/** 表格的key（id） */
		key: string;
	}

	/**
	 * 用户性别
	 * - 0: 女
	 * - 1: 男
	 */
	type GenderKey = NonNullable<User["gender"]>;

	/**
	 * 用户状态
	 * - 1: 启用
	 * - 2: 禁用
	 * - 3: 冻结
	 * - 4: 软删除
	 */
	type UserStatusKey = NonNullable<User["userStatus"]>;
}
declare namespace SupplierManagement {
	interface Supplier extends ApiSupplier.Supplier {
		/** 表格的key（id） */
		key: string;

		/** 供货模式;1抽佣2成本价 */
		supplyModeValue: string;

		/** 创建人 */
		createdBy: string;
		/** 创建时间 */
		createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		/** 最近更新时间 */
		updatedTime: number;
	}

	/**
	 * 供货类别;
	 * 1:电商;2:自营
	 */
	type SupplyCategoryKey = NonNullable<Supplier["supplyCategory"]>;
	// /**
	//  * 是否海外供应商
	//  * - 0: 否
	//  * - 1: 是
	//  */
	// type IsOverseasKey = NonNullable<Supplier["isOverseas"]>;

	/**
	 * 纳税人类型
	 * (0：非增值纳税人，1：一般纳税人，2：小规模纳税人)
	 */
	type TaxpayerTypeKey = NonNullable<Supplier["taxpayerType"]>;
	/**
	 * 是否有效
	 * (0：失效，1：有效)
	 */
	type ActiveFlagKey = NonNullable<Supplier["activeFlag"]>;
	/**
	 * 供货模式
	 * (1:内购自营;2:企业自供;3:内购分销)
	 */
	type SupplyModeKey = NonNullable<Supplier["supplyMode"]>;
	/**
	 * 供应商类型
	 * (1：国有;2：私企；3：外企)
	 */
	type supplierTypeKey = NonNullable<Supplier["supplierType"]>;
	/**
	 * 是否有效
	 * (0：失效，1：有效)
	 */
	type PointPaymentKey = NonNullable<Supplier["pointPayment"]>;
	/**
	 * 是否有海外购店铺
	 * (0:否;1:是)
	 */
	type HasCrossBorderShopKey = NonNullable<Supplier["hasCrossBorderShop"]>;
	/**
	 * 海外购类型
	 * (1保税仓2自提3直邮)
	 */
	type CrossBorderTypesKey = NonNullable<Supplier["crossBorderTypes"]>;
}
/** 字典项 */
declare namespace DictManagement {
	interface Dictionary extends ApiDictionary.Dictionary {
		/** 创建人 */
		createdBy?: string;
		/** 创建时间 */
		createdTime?: number;
		/** 最近更新人 */
		updatedBy?: string;
		/** 最近更新时间 */
		updatedTime?: number;
	}
	interface DictionaryData extends ApiDictionary.DictionaryData {
		/** 字典Key */
		dictName: string | null;
		/** 字典描述 */
		description: string | null;

		/** 创建人 */
		createdBy?: string;
		/** 创建时间 */
		createdTime?: number;
		/** 最近更新人 */
		updatedBy?: string;
		/** 最近更新时间 */
		updatedTime?: number;
	}

	/** 字典类型 1:系统类型;2:订单类型;3:商品类型;4:供应商类型;*/
	type DictTypeKey = NonNullable<Dictionary["dictType"]>;
}

declare namespace GoodsInfosManagement {
	interface GoodsInfos extends ApiGoodsInfos.GoodsInfos {
		/** 品牌名称 */
		brandName: string | null;
		/** 类目名称 */
		categoryName: string | null;
		/** 供应商名称 */
		supplierName: string | null;
		/** 供应商店铺名称 */
		supplierShopName: string | null;

		/** 创建人 */
		createdBy: string;
		/** 创建时间 */
		createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		/** 最近更新时间 */
		updatedTime: number;

		/**结算模式**/
		settlementModel?: number | string;
	}
	interface SupplierSku extends ApiGoodsInfos.SupplierSku {
		// /** 店铺名称 */
		// supplierShopName: string | number | null;

		/** 创建人 */
		createdBy: string;
		/** 创建时间 */
		createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		/** 最近更新时间 */
		updatedTime: number;
	}

	/**
	 * 商品类型;1:实物;2:虚拟商品
	 */
	type ItemTypeKey = NonNullable<GoodsInfos["itemType"]>;
	/**
	 * 出售状态(草稿（1）、审核中（2）、审核通过/审核不通过（3/4）、上架商城（5）、下架商城（6）
	 */
	type OnSaleKey = NonNullable<GoodsInfos["onSale"]>;
	/**
	 * 供应商上架状态；下架0;上架:1
	 */
	type supplierOnSaleKey = NonNullable<GoodsInfos["supplierOnSale"]>;

	/**
	 * 结算模式 1：成本价模式;2：抽佣模式
	 * **/
	type settlementModelKey = NonNullable<GoodsInfos["settlementModel"]>;
	/**
	 * 商品状态 待审核上架0 已上架1 释放待审核2
	 */
	type spuStatusKey = NonNullable<GoodsInfos["spuStatus"]>;
	/**
	 * 草稿:1;审核中:2;审核通过:3;审核不通过:4;审核失败:5;
	 */
	type ReviewStatusKey = NonNullable<GoodsInfos["reviewStatus"]>;

	interface GoodsSpac {
		id?: string;
		/** 规格类型 */
		specType: string;
		/** 规格类型名称 */
		specTypeName?: string;
		/** 规格值 */
		spacValue: string[];
		/** 规格值名称 */
		spacValueName?: GoodsSpacValues[] | [];
	}
	interface GoodsSpacTypes {
		id?: string;
		/** 分类ID */
		categoryId: string | number;
		/** 规格名 */
		specName: string;
		/** 规格Code */
		specCode: string;
	}
	interface GoodsSpacValues {
		id?: string;
		/** 规格组ID */
		specId: string | number;
		/** 规格值的名字 */
		name: string;
		/** 规格值的Code */
		specValCode: string;
	}
}
declare namespace COrderManagement {
	interface COrderInfos extends ApiCOrderInfos.COrderInfos {
		/** 下单人名称 */
		userName: string;
		/** 公司名称 */
		companyName: string;
		/** 店铺名 */
		shopName: string | null;
		/** 供应商名 */
		supplierName: string | null;

		/** 创建人 */
		createdBy: string;
		/** 创建时间 */
		createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		/** 最近更新时间 */
		updatedTime: number;
	}
	interface ReceiverInfo extends ApiCOrderInfos.ReceiverInfo {
		// /** 店铺名称 */
		// supplierShopName: string | number | null;

		/** 创建人 */
		createdBy: string;
		/** 创建时间 */
		createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		/** 最近更新时间 */
		updatedTime: number;
	}
	interface LogisticsResp extends ApiCOrderInfos.LogisticsResp {}

	/**
	 * 支付渠道 1, 微信支付；3, 积分支付；4, 积分微信混合支付
	 */
	type PayChannelKey = NonNullable<COrderInfos["payChannel"]>;
	/**
	 * 订单状态(0:未付款，1:已付款，2:待发货，5:待收货，6：已完成，7:已取消，8：支付异常，9：交易关闭，81：创单异常)
	 */
	type OrderStatusKey = NonNullable<COrderInfos["orderStatus"]>;
	/**
	 * 支付状态 （1:已取消 2:未支付 3:已支付 4:退款中 5:退款成功）
	 */
	type PaymentStatusKey = NonNullable<COrderInfos["paymentStatus"]>;
	/**
	 * 发货状态 (2:未发货 3:部分发货 5:全部发货 6:已收货 7:已取消)
	 */
	type DeliveryStatusKey = NonNullable<COrderInfos["deliveryStatus"]>;
	/**
	 * 售后状态 (0:全部 1:审核中 2:已拒绝 3:退款中 4:退款成功 5:待退货 6:退回中 7:已入库 8:拒绝入库 9:协商关单 10:已取消 11:商家寄回 12:换货成功)
	 */
	type AfterSalesStatusKey = NonNullable<COrderInfos["afterSalesStatus"]>;
	/**
	 * 退货状态(0:未退货，1:部分退货退款，2:全部退货退款)
	 */
	type WithdrawStatusKey = NonNullable<COrderInfos["withdrawStatus"]>;
	/**
	 * 是否预售
	 * **/
	type IsPreSaleKey = NonNullable<COrderInfos["isPreSale"]>;
	/**
	 * 是否是组合商品
	 */
	type skuTypeKey = NonNullable<COrderInfos["skuType"]>;
	/**
	 * 商城报关状态;0:未报关;1:报关成功；2:报关失败
	 */
	type MallCustomsDeclarationStatusKey = NonNullable<
		COrderInfos["mallCustomsDeclarationStatus"]
	>;
	/**
	 * 支付报关状态;0:未报关;1:报关成功；2:报关失败
	 */
	type PayCustomsDeclarationStatusKey = NonNullable<
		COrderInfos["payCustomsDeclarationStatus"]
	>;
	/**
	 * 仓库报关状态;0:未报关;1:报关成功；2:报关失败
	 */
	type WarehouseCustomsDeclarationStatussKey = NonNullable<
		COrderInfos["warehouseCustomsDeclarationStatus"]
	>;
	/**
	 * 快递类型，1.保宏 2.快递100
	 */
	type LogisticsTypeKey = NonNullable<LogisticsResp["logisticsType"]>;
	/**
	 * 运单状态，0在途，1揽收，2疑难，3签收，4退签，5派件，6退回，7转投
	 */
	type DeliveryBillStatusKey = NonNullable<LogisticsResp["deliveryBillStatus"]>;
}
declare namespace CCustomManagement {
	interface CCustomInfos extends ApiCCustomInfos.CCustomInfos {
		/** 创建人 */
		createdBy: string;
		// /** 创建时间 */
		// createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		// /** 最近更新时间 */
		// updatedTime: number;
	}
	interface CUser extends ApiCCustomInfos.CUser {
		/** 创建人 */
		createdBy?: string;
		// /** 创建时间 */
		// createdTime: number;
		/** 最近更新人 */
		updatedBy?: string;
		// /** 最近更新时间 */
		// updatedTime: number;
	}

	/**
	 * 密码状态;0:初始密码;1:密码已修改
	 */
	type PasswordStatusKey = NonNullable<CCustomInfos["passwordStatus"]>;
	/**
	 * 账号状态;0:初始化，2:启用；3：禁用 4注销 5 废弃;
	 */
	type AccountStatusKey = NonNullable<CCustomInfos["accountStatus"]>;
	/**
	 * 0:未知;1:企业新增;2:在线注册
	 */
	type RegisterChannelKey = NonNullable<CCustomInfos["registerChannel"]>;
	/**
	 * 性别(0：男，1：女)
	 */
	type CUserSexKey = NonNullable<CUser["sex"]>;
}
declare namespace PonitManagement {
	interface PointCompanyInfos extends ApiPonitInfos.PointCompanyInfos {
		/** 创建人 */
		createdBy: string;
		// /** 创建时间 */
		// createdTime: number;
		/** 最近更新人 */
		updatedBy: string;
		// /** 最近更新时间 */
		// updatedTime: number;
	}
	interface PointCompanyRules extends ApiPonitInfos.PointCompanyRules {
		/** 创建人 */
		createdBy?: string;
		// /** 创建时间 */
		// createdTime: number;
		/** 最近更新人 */
		updatedBy?: string;
		// /** 最近更新时间 */
		// updatedTime: number;
	}
	interface PointCompanyRecode extends ApiPonitInfos.PointCompanyRecode {
		/** 创建人 */
		createdBy?: string;
		// /** 创建时间 */
		// createdTime: number;
		/** 最近更新人 */
		updatedBy?: string;
		// /** 最近更新时间 */
		// updatedTime: number;
	}

	/**
	 * 流水类型
	 */
	type TransactionTypeKey = NonNullable<PointCompanyRecode["transactionType"]>;
}
