export const routeModel: Record<Auth.RoleType, AuthRoute.Route[]> = {
	super: [
		{
			name: "workbench_work_list",
			path: "/workbench/work/list",
			component: "self",
			meta: {
				title: "workbench",
				icon: "uil:equal-circle",
				keepAlive: true,
				requiresAuth: false,
				order: 1,
			},
		},
		{
			name: "bill_of_lading_list",
			path: "/bill_of_lading/list",
			component: "self",
			meta: {
				title: "B/L",
				icon: "uil:equal-circle",
				keepAlive: true,
				requiresAuth: false,
				order: 2,
			},
		},
	],
	admin: [
		{
			name: "workbench_work_list",
			path: "/workbench/work/list",
			component: "self",
			meta: {
				title: "workbench",
				icon: "uil:equal-circle",
				keepAlive: true,
				requiresAuth: false,
				order: 1,
			},
		},
		{
			name: "bill_of_lading_list",
			path: "/bill_of_lading/list",
			component: "self",
			meta: {
				title: "B/L",
				icon: "uil:equal-circle",
				keepAlive: true,
				requiresAuth: false,
				order: 2,
			},
		},
	],
	user: [
		{
			name: "workbench_work_list",
			path: "/workbench/work/list",
			component: "self",
			meta: {
				title: "workbench",
				icon: "uil:equal-circle",
				keepAlive: true,
				requiresAuth: false,
				order: 1,
			},
		},
		{
			name: "bill_of_lading_list",
			path: "/bill_of_lading/list",
			component: "self",
			meta: {
				title: "B/L",
				icon: "uil:equal-circle",
				keepAlive: true,
				requiresAuth: false,
				order: 2,
			},
		},
	],
};
