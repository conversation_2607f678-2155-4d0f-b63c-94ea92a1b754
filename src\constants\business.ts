export const loginModuleLabels: Record<UnionKey.LoginModule, string> = {
	"pwd-login": "账密登录",
	"code-login": "手机验证码登录",
	register: "注册",
	"reset-pwd": "重置密码",
	"bind-wechat": "微信绑定",
};

export const userRoleLabels: Record<Auth.RoleType, string> = {
	super: "超级管理员",
	admin: "管理员",
	user: "普通用户",
};
export const userRoleOptions: Common.OptionWithKey<Auth.RoleType>[] = [
	{ value: "super", label: userRoleLabels.super },
	{ value: "admin", label: userRoleLabels.admin },
	{ value: "user", label: userRoleLabels.user },
];

/** 通用是否选项 */
export const commonBoolLabels: Record<0 | 1 | "0" | "1", string> = {
	0: "否",
	1: "是",
};
export const commonBoolOptions: Common.OptionWithKey<0 | 1 | "0" | "1">[] = [
	{ value: "0", label: commonBoolLabels["0"] },
	{ value: "1", label: commonBoolLabels["1"] },
];

/** 状态（0停用 1正常） */
// export const CommonStatusLabels: Record<0 | 1 | "0" | "1", string> = {
// 	0: "停用",
// 	1: "正常",
// };
// export const CommonStatusOptions: Common.OptionWithKey<0 | 1 | "0" | "1">[] = [
// 	{ value: "0", label: CommonStatusLabels["0"] },
// 	{ value: "1", label: CommonStatusLabels["1"] },
// ];

export const CommonStatusLabels: Record<0 | 1 | 2 | "0" | "1" | "2", string> = {
	0: "初始化",
	1: "正常",
	2: "无效",
};
export const CommonStatusOptions: Common.OptionWithKey<
	0 | 1 | 2 | "0" | "1" | "2"
>[] = [
	{ value: "0", label: CommonStatusLabels["0"] },
	{ value: "1", label: CommonStatusLabels["1"] },
	{ value: "2", label: CommonStatusLabels["2"] },
];

/** 用户性别 */
export const genderLabels: Record<UserManagement.GenderKey, string> = {
	0: "女",
	1: "男",
};

export const genderOptions: Common.OptionWithKey<UserManagement.GenderKey>[] = [
	{ value: "0", label: genderLabels["0"] },
	{ value: "1", label: genderLabels["1"] },
];

/** 用户状态 */
export const userStatusLabels: Record<UserManagement.UserStatusKey, string> = {
	1: "启用",
	2: "禁用",
	3: "冻结",
	4: "软删除",
};
export const userStatusOptions: Common.OptionWithKey<UserManagement.UserStatusKey>[] =
	[
		{ value: "1", label: userStatusLabels["1"] },
		{ value: "2", label: userStatusLabels["2"] },
		{ value: "3", label: userStatusLabels["3"] },
		{ value: "4", label: userStatusLabels["4"] },
	];

/** 供货类型 */
export const supplyCategoryLabels: Record<
	SupplierManagement.SupplyCategoryKey,
	string
> = {
	1: "电商",
	2: "自营",
};
export const supplyCategoryOptions: Common.OptionWithKey<SupplierManagement.SupplyCategoryKey>[] =
	[
		{ value: "1", label: supplyCategoryLabels["1"] },
		{ value: "2", label: supplyCategoryLabels["2"] },
	];
// /** 是否海外供应商 */
// export const isOverseasLabels: Record<
// 	SupplierManagement.IsOverseasKey,
// 	string
// > = {
// 	0: "否",
// 	1: "是",
// };
// export const isOverseasOptions: Common.OptionWithKey<SupplierManagement.IsOverseasKey>[] =
// 	[
// 		{ value: "0", label: isOverseasLabels["0"] },
// 		{ value: "1", label: isOverseasLabels["1"] },
// 	];

/** 纳税人类型 */
export const taxpayerTypeLabels: Record<
	SupplierManagement.TaxpayerTypeKey,
	string
> = {
	0: "非增值纳税人",
	1: "一般纳税人",
	2: "小规模纳税人",
};
export const taxpayerTypeOptions: Common.OptionWithKey<SupplierManagement.TaxpayerTypeKey>[] =
	[
		{ value: "0", label: taxpayerTypeLabels["0"] },
		{ value: "1", label: taxpayerTypeLabels["1"] },
		{ value: "2", label: taxpayerTypeLabels["2"] },
	];

/** 是否有效 */
export const activeFlagLabels: Record<
	SupplierManagement.ActiveFlagKey,
	string
> = {
	0: "失效",
	1: "有效",
};
export const activeFlagOptions: Common.OptionWithKey<SupplierManagement.ActiveFlagKey>[] =
	[
		{ value: "0", label: activeFlagLabels["0"] },
		{ value: "1", label: activeFlagLabels["1"] },
	];

/** 供货模式 */
export const supplyModeLabels: Record<
	SupplierManagement.SupplyModeKey,
	string
> = {
	1: "抽佣",
	2: "成本价",
};
export const supplyModeOptions: Common.OptionWithKey<SupplierManagement.SupplyModeKey>[] =
	[
		{ value: "1", label: supplyModeLabels["1"] },
		{ value: "2", label: supplyModeLabels["2"] },
	];
/** 供货模式 */
export const supplierTypeLabels: Record<
	SupplierManagement.supplierTypeKey,
	string
> = {
	// '1': "国有",
	// '2': "私企",
	// '3': "外企",
	"2": "外部",
};
export const supplierTypeOptions: Common.OptionWithKey<SupplierManagement.supplierTypeKey>[] =
	[
		// { value: 1, label: supplierTypeLabels["1"] },
		{ value: 2, label: supplierTypeLabels["2"] },
		// { value: 3, label: supplierTypeLabels["3"] },
	];

/** 货币类型*/
export const moneyLabels: Record<moneyType.moneyTypes, string> = {
	"1": "人民币",
	"2": "港币",
	"3": "美元",
};
export const moneyOptions: Common.OptionWithKey<moneyType.moneyTypes>[] = [
	{ value: 1, label: moneyLabels["1"] },
	{ value: 2, label: moneyLabels["2"] },
	{ value: 3, label: moneyLabels["3"] },
];

/** 企业类别*/
export const comCategoryLabels: Record<
	businessCategory.businessCategoryTypes,
	string
> = {
	"1": "组织",
	"2": "组",
	"3": "个人",
};
export const comCategoryOptions: Common.OptionWithKey<businessCategory.businessCategoryTypes>[] =
	[
		{ value: 1, label: comCategoryLabels["1"] },
		{ value: 2, disabled: true, label: comCategoryLabels["2"] },
		{ value: 3, disabled: true, label: comCategoryLabels["3"] },
	];

/** 企业分组*/
export const businessGroupLabels: Record<
	businessGroup.businesssGroupTypes,
	string
> = {
	"1": "外部客商",
	"2": "送达方",
	"3": "招商局集团内部合作伙伴",
	// '4': "海通集团内部合部作伙伴",
	"4": "海通集团内部合作伙伴",
	"5": "海通控股合作伙伴",
	"6": "员工",
	"7": "联系人",
	"8": "一次性客商",
};
export const businessGroupOptions: Common.OptionWithKey<businessGroup.businesssGroupTypes>[] =
	[
		{ value: 1, label: businessGroupLabels["1"] },
		{ value: 2, disabled: true, label: businessGroupLabels["2"] },
		// { value: 3,disabled:true, label: businessGroupLabels["3"] },
		{ value: 3, label: businessGroupLabels["3"] },
		{ value: 4, disabled: true, label: businessGroupLabels["4"] },
		{ value: 5, disabled: true, label: businessGroupLabels["5"] },
		{ value: 6, disabled: true, label: businessGroupLabels["6"] },
		{ value: 7, disabled: true, label: businessGroupLabels["7"] },
		{ value: 8, disabled: true, label: businessGroupLabels["8"] },
	];

/** 企业类型*/
export const businessTypeLabels: Record<businessType.businessType, string> = {
	"1": "国有",
	"2": "私企",
	"3": "外企",
};
export const businessTypeOptions: Common.OptionWithKey<businessType.businessType>[] =
	[
		{ value: 1, label: businessTypeLabels["1"] },
		{ value: 2, label: businessTypeLabels["2"] },
		{ value: 3, label: businessTypeLabels["3"] },
	];
/** 客商等级*/
export const customerRankLabels: Record<customerRank.customerRanks, string> = {
	"1": "优惠客户，信用度高",
	"2": "信用状况相当良好",
	"3": "一般客户，偿债和信用状况一般",
	"4": "信用较差，企业诚信度较低，资产质量较差",
	"5": "不合格客户，信用极差，资质无法保证",
	"6": "坏账客户，终止开展业务",
};
export const customerRankOptions: Common.OptionWithKey<customerRank.customerRanks>[] =
	[
		{ value: 1, label: customerRankLabels["1"] },
		{ value: 2, label: customerRankLabels["2"] },
		{ value: 3, label: customerRankLabels["3"] },
		{ value: 4, label: customerRankLabels["4"] },
		{ value: 5, label: customerRankLabels["5"] },
		{ value: 6, label: customerRankLabels["6"] },
	];
/** 是否支持积分支付:1-支持，0-不支持 */
export const pointPaymentLabels: Record<
	SupplierManagement.PointPaymentKey,
	string
> = {
	0: "不支持",
	1: "支持",
};
export const pointPaymentOptions: Common.OptionWithKey<SupplierManagement.PointPaymentKey>[] =
	[
		{ value: "0", label: pointPaymentLabels["0"] },
		{ value: "1", label: pointPaymentLabels["1"] },
	];
/** 是否有海外购店铺 */
export const hasCrossBorderShopLabels: Record<
	SupplierManagement.HasCrossBorderShopKey,
	string
> = {
	0: "否",
	1: "是",
};
export const hasCrossBorderShopOptions: Common.OptionWithKey<SupplierManagement.HasCrossBorderShopKey>[] =
	[
		{ value: "0", label: hasCrossBorderShopLabels["0"] },
		{ value: "1", label: hasCrossBorderShopLabels["1"] },
	];
/** 海外购类型 1保税仓2自提3直邮 */
export const crossBorderTypesLabels: Record<
	SupplierManagement.CrossBorderTypesKey,
	string
> = {
	1: "保税仓",
	2: "自提",
	3: "直邮",
};
export const crossBorderTypesOptions: Common.OptionWithKey<SupplierManagement.CrossBorderTypesKey>[] =
	[
		{ value: "1", label: crossBorderTypesLabels["1"] },
		{ value: "2", label: crossBorderTypesLabels["2"] },
		{ value: "3", label: crossBorderTypesLabels["3"] },
	];
/** 字典类型 1:系统类型;2:订单类型;3:商品类型;4:供应商类型;*/
export const DictTypeLabels: Record<DictManagement.DictTypeKey, string> = {
	1: "系统类型",
	2: "订单类型",
	3: "商品类型",
	4: "供应商类型",
};
export const DictTypeOptions: Common.OptionWithKey<DictManagement.DictTypeKey>[] =
	[
		{ value: "1", label: DictTypeLabels["1"] },
		{ value: "2", label: DictTypeLabels["2"] },
		{ value: "3", label: DictTypeLabels["3"] },
		{ value: "4", label: DictTypeLabels["4"] },
	];

/** 商品状态 待审核上架0 已上架1 释放待审核2 */
export const spuStatusLabels: Record<
	GoodsInfosManagement.spuStatusKey,
	string
> = {
	0: "待审核上架",
	1: "已上架",
	2: "释放待审核",
};
export const spuStatusOptions: Common.OptionWithKey<GoodsInfosManagement.spuStatusKey>[] =
	[
		{ value: "0", label: spuStatusLabels["0"] },
		{ value: "1", label: spuStatusLabels["1"] },
		{ value: "2", label: spuStatusLabels["2"] },
	];
/** 下架 0;上架:1 */
export const supplierOnSaleLabels: Record<
	GoodsInfosManagement.supplierOnSaleKey,
	string
> = {
	0: "下架",
	1: "上架",
};
export const supplierOnSaleOptions: Common.OptionWithKey<GoodsInfosManagement.supplierOnSaleKey>[] =
	[
		{ value: 0, label: supplierOnSaleLabels["0"] },
		{ value: 1, label: supplierOnSaleLabels["1"] },
	];

/** 结算模式 1：成本价模式;2：抽佣模式**/
export const settlementModelLabels: Record<
	GoodsInfosManagement.settlementModelKey,
	string
> = {
	1: "成本价模式",
	2: "抽佣模式",
};
export const settlementModelOptions: Common.OptionWithKey<GoodsInfosManagement.settlementModelKey>[] =
	[
		{ value: 1, label: settlementModelLabels["1"] },
		{ value: 2, label: settlementModelLabels["2"] },
	];

/** 下架 0;上架:1 */
export const onSaleLabelsGroupGoods: Record<
	GoodsInfosManagement.OnSaleKey,
	string
> = {
	0: "下架",
	1: "上架",
	2: "",
};
/** 草稿:1;审核中:2;审核通过:3;审核不通过:4;审核失败:5; */
export const reviewStatusLabels: Record<
	GoodsInfosManagement.ReviewStatusKey,
	string
> = {
	// 1: "草稿",
	2: "审核中",
	3: "审核通过",
	4: "审核不通过",
	5: "审核失败",
};
export const reviewStatusOptions: Common.OptionWithKey<GoodsInfosManagement.ReviewStatusKey>[] =
	[
		// { value: "1", label: reviewStatusLabels["1"] },
		{ value: "2", label: reviewStatusLabels["2"] },
		{ value: "3", label: reviewStatusLabels["3"] },
		{ value: "4", label: reviewStatusLabels["4"] },
		{ value: "5", label: reviewStatusLabels["5"] },
	];

/**
 * 支付渠道 1, 微信支付；2, 支付宝支付；3, 积分支付；4, 积分微信混合支付；5,积分支付宝混合支付；6, 一网通支付； 7,积分一网通混合支付
 */
export const payChannelLabels: Record<COrderManagement.PayChannelKey, string> =
	{
		1: "微信支付",
		2: "一网通支付宝支付",
		3: "积分支付",
		4: "积分微信混合支付",
		5: "积分支付宝混合支付",
		6: "一网通支付",
		7: "积分一网通混合支付",
		8: "工行支付",
		9: "积分工行混合支付",
		10: "中免支付",
		11: "小程序微信支付",
	};
export const payChannelOptions: Common.OptionWithKey<COrderManagement.PayChannelKey>[] =
	[
		{ value: "1", label: payChannelLabels["1"] },
		{ value: "2", label: payChannelLabels["2"] },
		{ value: "3", label: payChannelLabels["3"] },
		{ value: "4", label: payChannelLabels["4"] },
		{ value: "5", label: payChannelLabels["5"] },
		{ value: "6", label: payChannelLabels["6"] },
		{ value: "7", label: payChannelLabels["7"] },
		{ value: "8", label: payChannelLabels["8"] },
		{ value: "9", label: payChannelLabels["9"] },
		{ value: "10", label: payChannelLabels["10"] },
		{ value: "11", label: payChannelLabels["11"] },
	];
/** 订单状态(0:未付款，1:已付款 2:待发货; 5:待收货; 6:已完成，7:已取消 9:交易关闭 81:创单失败)	 */
export const orderStatusLabels: Record<
	COrderManagement.OrderStatusKey,
	string
> = {
	0: "未付款",
	1: "已付款",
	2: "待发货",
	3: "部分发货",
	5: "待收货",
	6: "已完成",
	7: "已取消",
	8: "交易异常",
	9: "交易关闭",
	81: "创单失败",
};
export const orderStatusOptions: Common.OptionWithKey<COrderManagement.OrderStatusKey>[] =
	[
		{ value: "0", label: orderStatusLabels["0"] },
		{ value: "1", label: orderStatusLabels["1"] },
		{ value: "2", label: orderStatusLabels["2"] },
		{ value: "3", label: orderStatusLabels["3"] },
		{ value: "5", label: orderStatusLabels["5"] },
		{ value: "6", label: orderStatusLabels["6"] },
		{ value: "7", label: orderStatusLabels["7"] },
		{ value: "8", label: orderStatusLabels["8"] },
		{ value: "9", label: orderStatusLabels["9"] },
		{ value: "81", label: orderStatusLabels["81"] },
	];

/** 支付状态 （0:支付中 1:支付成功 2:支付失败 3:支付异常）	 */
export const paymentStatusLabels: Record<
	COrderManagement.PaymentStatusKey,
	string
> = {
	0: "支付中",
	1: "支付成功",
	2: "支付失败",
	3: "支付异常",
};
export const paymentStatusOptions: Common.OptionWithKey<COrderManagement.PaymentStatusKey>[] =
	[
		{ value: "0", label: paymentStatusLabels["0"] },
		{ value: "1", label: paymentStatusLabels["1"] },
		{ value: "2", label: paymentStatusLabels["2"] },
		{ value: "3", label: paymentStatusLabels["3"] },
	];

/** 发货状态 (2:未发货 3:部分发货 5:全部发货 6:已收货 7:已取消)	 */
export const deliveryStatusLabels: Record<
	COrderManagement.DeliveryStatusKey,
	string
> = {
	2: "未发货",
	3: "部分发货",
	5: "全部发货",
	6: "已收货",
	7: "已取消",
};
export const deliveryStatusOptions: Common.OptionWithKey<COrderManagement.DeliveryStatusKey>[] =
	[
		{ value: "2", label: deliveryStatusLabels["2"] },
		{ value: "3", label: deliveryStatusLabels["3"] },
		{ value: "5", label: deliveryStatusLabels["5"] },
		{ value: "6", label: deliveryStatusLabels["6"] },
		{ value: "7", label: deliveryStatusLabels["7"] },
	];
/** 进入结算状态 (0:未进入结算 1:已进入结算 )	 */
export const toSettlementStatusLabels: Record<0 | 1 | "0" | "1", string> = {
	0: "未进入结算",
	1: "已进入结算",
};
export const toSettlementStatusOptions: Common.OptionWithKey<
	0 | 1 | "0" | "1"
>[] = [
	{ value: "0", label: toSettlementStatusLabels["0"] },
	{ value: "1", label: toSettlementStatusLabels["1"] },
];
/** 结算类型 (0:应收 1:应付 )	 */
export const settlementTypeLabels: Record<0 | 1 | "0" | "1", string> = {
	0: "应收",
	1: "应付",
};
export const settlementTypeOptions: Common.OptionWithKey<0 | 1 | "0" | "1">[] =
	[
		{ value: "0", label: settlementTypeLabels["0"] },
		{ value: "1", label: settlementTypeLabels["1"] },
	];

/** 售后状态 (0:全部 1:审核中 2:已拒绝 3:退款中 4:退款成功 5:待退货 6:退回中 7:已入库 8:拒绝入库 9:协商关单 10:已取消 11:商家寄回 12:换货成功)	 */
export const afterSalesStatusLabels: Record<
	COrderManagement.AfterSalesStatusKey,
	string
> = {
	0: "全部",
	1: "审核中",
	2: "已拒绝",
	3: "退款中",
	4: "退款成功",
	5: "待退货",
	6: "退回中",
	7: "已入库",
	8: "拒绝入库",
	9: "协商关单",
	10: "已取消",
	11: "商家寄回",
	12: "换货成功",
	13: "已关单",
};
export const afterSalesStatusOptions: Common.OptionWithKey<COrderManagement.AfterSalesStatusKey>[] =
	[
		{ value: "0", label: afterSalesStatusLabels["0"] },
		{ value: "1", label: afterSalesStatusLabels["1"] },
		{ value: "2", label: afterSalesStatusLabels["2"] },
		{ value: "3", label: afterSalesStatusLabels["3"] },
		{ value: "4", label: afterSalesStatusLabels["4"] },
		{ value: "5", label: afterSalesStatusLabels["5"] },
		{ value: "6", label: afterSalesStatusLabels["6"] },
		{ value: "7", label: afterSalesStatusLabels["7"] },
		{ value: "8", label: afterSalesStatusLabels["8"] },
		{ value: "9", label: afterSalesStatusLabels["9"] },
		{ value: "10", label: afterSalesStatusLabels["10"] },
		{ value: "11", label: afterSalesStatusLabels["11"] },
		{ value: "12", label: afterSalesStatusLabels["12"] },
		{ value: "13", label: afterSalesStatusLabels["13"] },
	];

/** 退货状态; 0:未售后;2:部分售后;3:全部售后;4:售后中	 */
export const withdrawStatusLabels: Record<
	COrderManagement.WithdrawStatusKey,
	string
> = {
	0: "未售后",
	2: "部分售后",
	3: "全部售后",
	// 4: "售后中",
};
export const withdrawStatusOptions: Common.OptionWithKey<COrderManagement.WithdrawStatusKey>[] =
	[
		{ value: "0", label: withdrawStatusLabels["0"] },
		{ value: "2", label: withdrawStatusLabels["2"] },
		{ value: "3", label: withdrawStatusLabels["3"] },
		// { value: "4", label: withdrawStatusLabels["4"] },
	];

/** 是否预售：0：否 1：是**/
export const isPreSaleLabels: Record<COrderManagement.IsPreSaleKey, string> = {
	0: "否",
	1: "是",
};
export const isPreSaleOptions: Common.OptionWithKey<COrderManagement.IsPreSaleKey>[] =
	[
		{ value: "0", label: isPreSaleLabels["0"] },
		{ value: "1", label: isPreSaleLabels["1"] },
	];

/** 是否是组合商品：0：否 1：是**/
export const skuTypeLabels: Record<COrderManagement.skuTypeKey, string> = {
	1: "否",
	2: "是",
};
export const skuTypeOptions: Common.OptionWithKey<COrderManagement.skuTypeKey>[] =
	[
		{ value: "1", label: skuTypeLabels["1"] },
		{ value: "2", label: skuTypeLabels["2"] },
	];

/**
 * 运单状态，0在途，1揽收，2疑难，3签收，4退签，5派件，6退回，7转投
 */
export const deliveryBillStatusLabels: Record<
	COrderManagement.DeliveryBillStatusKey,
	string
> = {
	0: "在途",
	1: "揽收",
	2: "疑难",
	3: "签收",
	4: "退签",
	5: "派件",
	6: "退回",
	7: "转投",
};
export const deliveryBillStatusOptions: Common.OptionWithKey<COrderManagement.DeliveryBillStatusKey>[] =
	[
		{ value: "0", label: deliveryBillStatusLabels["0"] },
		{ value: "1", label: deliveryBillStatusLabels["1"] },
		{ value: "2", label: deliveryBillStatusLabels["2"] },
		{ value: "3", label: deliveryBillStatusLabels["3"] },
		{ value: "4", label: deliveryBillStatusLabels["4"] },
		{ value: "5", label: deliveryBillStatusLabels["5"] },
		{ value: "6", label: deliveryBillStatusLabels["6"] },
		{ value: "7", label: deliveryBillStatusLabels["7"] },
	];
/**
 * 快递类型，1.保宏 2.快递100
 */
export const logisticsTypeLabels: Record<
	COrderManagement.LogisticsTypeKey,
	string
> = {
	1: "保宏",
	2: "快递100",
};
export const logisticsTypeOptions: Common.OptionWithKey<COrderManagement.LogisticsTypeKey>[] =
	[
		{ value: "1", label: logisticsTypeLabels["1"] },
		{ value: "2", label: logisticsTypeLabels["2"] },
	];

/** 密码状态;0:初始密码;1:密码已修改	 */
export const passwordStatusLabels: Record<
	CCustomManagement.PasswordStatusKey,
	string
> = {
	0: "初始密码",
	1: "密码已修改",
};
export const passwordStatusOptions: Common.OptionWithKey<CCustomManagement.PasswordStatusKey>[] =
	[
		{ value: "0", label: passwordStatusLabels["0"] },
		{ value: "1", label: passwordStatusLabels["1"] },
	];
/** 账号状态;0:初始化，2:启用；3：禁用 4注销 5 废弃;	 */
export const accountStatusLabels: Record<
	CCustomManagement.AccountStatusKey,
	string
> = {
	0: "初始化",
	2: "启用",
	3: "禁用",
	4: "注销",
	5: "废弃",
};
export const accountStatusOptions: Common.OptionWithKey<CCustomManagement.AccountStatusKey>[] =
	[
		{ value: "0", label: accountStatusLabels["0"] },
		{ value: "2", label: accountStatusLabels["2"] },
		{ value: "3", label: accountStatusLabels["3"] },
		{ value: "4", label: accountStatusLabels["4"] },
		{ value: "5", label: accountStatusLabels["5"] },
	];
/** 0:未知;1:企业新增;2:在线注册	 */
export const registerChannelLabels: Record<
	CCustomManagement.RegisterChannelKey,
	string
> = {
	0: "未知",
	1: "企业新增",
	2: "在线注册",
};
export const registerChannelOptions: Common.OptionWithKey<CCustomManagement.RegisterChannelKey>[] =
	[
		{ value: 0, label: registerChannelLabels["0"] },
		{ value: 1, label: registerChannelLabels["1"] },
		{ value: 2, label: registerChannelLabels["2"] },
	];
/** 性别(0：男，1：女)	 */
export const cUserSexLabels: Record<CCustomManagement.CUserSexKey, string> = {
	0: "男",
	1: "女",
};
export const cUserSexOptions: Common.OptionWithKey<CCustomManagement.CUserSexKey>[] =
	[
		{ value: "0", label: cUserSexLabels["0"] },
		{ value: "1", label: cUserSexLabels["1"] },
	];
