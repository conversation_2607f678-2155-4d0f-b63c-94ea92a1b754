<template>
	<router-link :to="routeHomePath" class="flex-center w-full nowrap-hidden">
		<system-logo class="text-22px text-primary" />
		<h2 v-show="showTitle" class="pl-8px text-16px font-bold text-primary transition duration-300 ease-in-out">
			SMART TRACK
			<!-- 福有招运营管理软件V2.0 -->
		</h2>
	</router-link>
</template>

<script setup lang="ts">
import { routePath } from '@/router';

defineOptions({ name: 'GlobalLogo' });

interface Props {
	/** 显示名字 */
	showTitle: boolean;
}

defineProps<Props>();

const routeHomePath = routePath('root');
</script>

<style scoped></style>
