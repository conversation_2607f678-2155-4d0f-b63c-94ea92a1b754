import { request } from "../request";



export const fetchTrackList = async (params: any) => {
	const data = await request.post("/track/page", params);
	return data;
};

export const fetchTrackAdd = async (params: any) => {
	const data = await request.post("/track/add", params);
	return data;
};
export const fetchTrackEdit= async (params: any) => {
	const data = await request.post("/track/update", params);
	return data;
};
export const fetchDeleteBatchIds= async (params: any) => {
	const data = await request.post("/track/deleteBatchIds", params);
	return data;
};
export const fetchArchiveBatch= async (params: any) => {
	const data = await request.post("/track/archiveBatch", params);
	return data;
};