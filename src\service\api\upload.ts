import { request } from "../request";

/** 上传文件 */
export const fetchUpload = async (params: any, config?: any) => {
	const data = await request.post("/api/file/upload", params, {
		headers: { "Content-Type": "multipart/form-data;" },
		...config,
	});
	return data;
};
export const fetchUploadZip = async (params: any, config?: any) => {
	const data = await request.post("/admin-file/upload-image", params, {
		headers: { "Content-Type": "multipart/form-data;" },
		...config,
	});
	return data;
};
/** 获取文件绝对地址 */
export const fetchFileAbsoUrl = async (params: any) => {
	const data = await request.post("/admin-file/query-absolute-url", params);
	return data;
};

/** 上传报文回执文件,多文件上传  files */
export const fetchXMLUpload = async (params: any, config?: any) => {
	const data = await request.post("/admin-customs/import", params, {
		headers: { "Content-Type": "multipart/form-data;" },
		...config,
	});
	return data;
};

/** 导出报文回执文件 */
export const fetchXMLExport = async (params: any, config?: any) => {
	const data = await request.post("/admin-customs/export", params, {
		headers: {
			"Content-Type": "application/x-www-form-urlencoded;",
		},
		responseType: "blob",
		...config,
	});
	return data;
};


