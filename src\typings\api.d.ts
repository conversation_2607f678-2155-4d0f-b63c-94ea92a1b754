// 后端接口返回的数据类型

/** 后端返回的用户权益相关类型 */
declare namespace ApiAuth {
	/** 返回的token和刷新token */
	interface Token {
		adminAccount: any;
		tokenInfo: any;
		token: string;
		refreshToken: string;
	}
	/** 返回的用户信息 */
	type UserInfo = Auth.UserInfo;
}

/** 后端返回的路由相关类型 */
declare namespace ApiData {
	/** 后端返回的路由数据类型 */
	interface tableList<T> {
		/** 列表总条数 */
		total: number;
		/** 当前第几页 */
		current: number;
		/** 列表数据 */
		list: T[] | null;
	}
}
/** 后端返回的路由相关类型 */
declare namespace ApiRoute {
	/** 后端返回的路由数据类型 */
	interface Route {
		/** 动态路由 */
		routes: AuthRoute.Route[];
		/** 路由首页对应的key */
		home: AuthRoute.AllRouteKey;
	}
}

declare namespace ApiUserManagement {
	interface User {
		/** 用户id */
		id: string;
		/** 用户名 */
		userName: string | null;
		/** 用户年龄 */
		age: number | null;
		/**
		 * 用户性别
		 * - 0: 女
		 * - 1: 男
		 */
		gender: "0" | "1" | null;
		/** 用户手机号码 */
		phone: string;
		/** 用户邮箱 */
		email: string | null;
		/**
		 * 用户状态
		 * - 1: 启用
		 * - 2: 禁用
		 * - 3: 冻结
		 * - 4: 软删除
		 */
		userStatus: "1" | "2" | "3" | "4" | null;
	}
}
/** 货币类型*/
declare namespace ApiMoneyType {
	interface moneyType {
		moneyType: 1 | 2 | 3 | null;
	}
}
/** 供应商基础数据 */
declare namespace ApiSupplier {
	interface Supplier {
		/** 用户id */
		id?: string;
		selectAddress: any;
		/** 供应商名称 */
		supplierName: string | null;
		level1PlaceCode: string;
		level1PlaceName: string;
		level2PlaceCode: string;
		level2PlaceName: string;
		level3PlaceCode: string;
		level3PlaceName: string;
		/** 供应商简称 */
		supplierShortName: string | null;
		/** 店铺名称 */
		shopName: string | null;
		/** 营业执照 */
		businessLicense?: string | null;
		/** 法人 */
		legalPresen?: string | null;
		/** 联系人 */
		contacts: string | null;
		/** 电话 */
		phone: string;
		/** 	邮箱 */
		email?: string | null;
		/** 供应商类型 1:电商 2:自营*/
		supplyCategory: "1" | "2";
		/** 供应商类型1：国有;2：私企；3：外企*/
		// supplierType: "1" | "2" | "3" | 1 | 2 | 3 | null;
		supplierType: "2" | 2 | null;
		// /** 是否海外供应商(0：否，1：是) */
		// isOverseas: "0" | "1";
		/** 供应商简介 */
		briefIntroduce?: string | null;
		/** 经营范围 */
		businessScope?: string | null;
		/** 注册资产 */
		registeredAssets?: string | null;
		/** 组织机构代码 */
		structureCode: string | null;
		/** 纳税人类型(0：非增值纳税人，1：一般纳税人，2：小规模纳税人) */
		taxpayerType: "0" | "1" | "2";
		/** 税务登记号 */
		taxpayerNumber?: string | null;
		/** 是否有效(0：失效，1：有效) */
		activeFlag?: "0" | "1";
		/** 备注 */
		remark?: string | null;
		/** 供应商地址 */
		address?: string | null;
		/** S4财务系统供应商编码 */
		s4SupplierCode?: string | null;
		/** 附件信息 */
		attach?: string | null;
		/** 供货模式;1抽佣2成本价 */
		supplyMode: "1" | "2";
		/** 是否有海外购店铺(0:否;1:是)	 */
		hasCrossBorderShop?: "0" | "1";
		/** 是否支持积分支付:1-支持，0-不支持	 */
		pointPayment: "0" | "1" | 0 | 1;
		/** 海外购类型(1保税仓2自提3直邮) */
		crossBorderTypes?: "1" | "2" | "3";
		/** 简介 */
		desc?: string | null;
		/** 登录名 */
		account?: string | null;
		/** 密码 */
		password?: string | null;
		/** 重置密码 */
		newPwd?: string;
		/** 结算对接人*/
		settlementContact?: string | null;
		/** 对接人联系方式*/
		settlementContactInfo?: string | null;
		/** 对接人邮箱*/
		settlementContactEmail?: string | null;
		/** 我方结算对接人*/
		ourSettlementContact?: string | null;
		/** 我方结算对接人邮箱*/
		ourSettlementContactEmail?: string | null;
		accountBankName?: string | null; //开户行名称
		currency?: string | null; //币种
		channel?: string | null; //上架渠道
		/** 供应商代码MDM*/
		mdmCode: string | null;
		bankAccount: string | null;
		bankCode: string | null;
		taxRegisterNo: string | null;
	}
}
/** 字典项基础数据 */
declare namespace ApiDictionary {
	interface Dictionary {
		/** 字典主键id */
		id?: string;
		/** 字典Key */
		dictName: string | null;
		/** 字典描述 */
		description: string | null;
		/** 字典类型 1:系统类型;2:订单类型;3:商品类型;4:供应商类型;*/
		dictType: "1" | "2" | "3" | "4";
		/** 状态（0停用 1正常） */
		status: "0" | "1";
		/** 备注 */
		remark?: string | null;
		/** 对应key值 */
		dictData: DictionaryData[];
	}
	interface DictionaryData {
		/** 字典编码id */
		id?: string;
		/** 字典标签 */
		dictLabel: string | null;
		/** 字典键值 */
		dictValue: string | null;
		/** 字典枚举ID */
		dictTypeId: string | null;
		/** 状态（0停用 1正常） */
		status: "0" | "1";
		/** 备注 */
		remark?: string | null;
	}
}
/** 商品信息基础数据 */
declare namespace ApiGoodsInfos {
	interface GoodsInfos {
		/** 商品id */
		id?: string;
		/** 商品名称 */
		name: string | null;
		/** SPU编码 */
		code: string | null;
		/** 店铺ID */
		supplierShopId: string | number | null;
		/** 类目ID */
		categoryId: string | number | null;
		/** 品牌ID */
		brandId: string | number | null;
		/** 供货编码 */
		supplyContractCode: string | null;
		/** 供应商编码 */
		supplierId: string | number | null;
		/** 列表图片 */
		mainImageUrl: string | Array<string> | null;
		/** 供应商category	 */
		supplierCategoryId?: string | number | null;
		/** 净价（不含税价格） */
		minCleanPrice: string | number | null;
		/** 供货产地 */
		origin: string | null;
		/** 配送方式；引用字典表	 */
		shippingType: string | number | null;
		/** 计量单位；引用字典表	 */
		unitMeasurement: string | number | null;
		/** 体积 */
		volume: string | null;
		/** 重量 */
		weight: string | null;
		/** 商品类型;1:实物;2:虚拟商品 */
		itemType: string | number | null;
		/** 售后类型；取字典表	 */
		afterSalesType?: string | number | null;
		/** 超售*/
		// overSell?: string | number | null;
		/** 是否跨境商品（0：否;1:是）*/
		isCrossBorderItem: 0 | 1 | "0" | "1";
		/** 发票类型;1:专用发票	 */
		invoiceType: 1 | "1";
		/** 商品详情	 */
		desc?: string | null;
		/** spu的所有规格组：{"内存":["128g","256g"] , "颜色":["蓝色","红色"]} */
		specInfo: string | object | null;
		/**下架0;上架:1 */
		onSale: 2 | 1 | 0 | "0" | "1" | "2";
		/**商品状态 待审核上架0 已上架1 释放待审核2 */
		spuStatus: 2 | 1 | 0 | "0" | "1" | "2";
		/**供应商上架状态: 下架0;上架:1 */
		supplierOnSale: 0 | 1 | "1" | "0";
		/**草稿:1;审核中:2;审核通过:3;审核不通过:4;审核失败:5; */
		reviewStatus: 2 | 3 | 4 | 5 | "2" | "3" | "4" | "5";
		/** SKU列表 */
		supplierSkuTabList: SupplierSku[] | [];
		/** 规格组表响应参数 */
		SpecTabForItemRespList: SpecTabItem[] | [];

		/** 结算模式**/
		settlementModel?: 2 | 1 | "1" | "2";
		/**抽佣比例**/
		commissionRate?: number;
	}

	/** SKU信息 */
	interface SupplierSku {
		/** SKU ID */
		id?: string;
		/** SKU编码 */
		code: string | null;
		/** S4编码 */
		s4Code: string | null;
		/** 外部商品编码 */
		outerSysSkuCode: string | null;
		/** 图片 */
		imagesUrl?: string | Array<string> | null;
		/** sku的具体规格组：{"内存:"128g","颜色:"蓝色"} */
		specInfo: string | object | null;
		/** 重量 */
		weight?: number | null;
		/** 库存数量 */
		stockSum: number | null;
		/** 含税价格 */
		taxPrice: number | null;
		/** 市场价 */
		marketPrice: number | null;
		/** 税收编码 */
		taxCode: number | null;
		/** 税率 */
		taxRate: number | null;
		/** 净价（不含税价格） */
		cleanPrice: number | null;
		/** 税额 */
		tax: number | null;
	}
	/** 规格组表响应参数 */
	interface SpecTabItem {
		/** 规格组 ID */
		id?: string;
		/** 	规格名 */
		specName: string | null;
		/** 规格编码 */
		specCode: string | null;
		/** 规格值表响应参数 */
		specValueTabList?: SpecValueItem[] | [];
	}
	/** 规格值表响应参数 */
	interface SpecValueItem {
		/** 主键ID */
		id?: string;
		/** 规格值的名字 */
		name: string | null;
		/** 规格组ID */
		specId: string | null;
		/** 规格值编码 */
		specValCode: string | null;
	}
}
/** C端订单信息基础数据 */
declare namespace ApiCOrderInfos {
	interface COrderInfos {
		/**  C端订单id */
		id?: string;
		/** 订单金额 */
		totalAmount: string | number | null;
		/** 支付编号 */
		pmtPaymentNo: string | number | null;
		/** 订单编号 */
		orderNo: string | null;
		/** 店铺ID */
		shopId: string | number | null;
		/** 购买人ID */
		userId: string | number | null;
		/** 购买人公司ID */
		companyId: string | number | null;
		/** 供应商ID */
		supplierId: string | number | null;
		/** 运费金额 */
		logisticsFee: string | number | null;
		/** 支付渠道 1, 微信支付；2, 支付宝支付；3, 积分支付；4, 积分微信混合支付；5,积分支付宝混合支付；6, 一网通支付； 7,积分一网通混合支付 */
		payChannel?:
			| 1
			| 2
			| 3
			| 4
			| 5
			| 6
			| 7
			| 8
			| 9
			| 10
			| "1"
			| "2"
			| "3"
			| "4"
			| "5"
			| "6"
			| "7"
			| "8"
			| "9"
			| "10"
			| "11";
		/** 商品总价 */
		totalPriceOfGoods: string | number | null;
		/** 支付金额 */
		payAmount: string | number | null;
		/** 积分 */
		payPoint: string | number | null;
		/** 报关人ID */
		customsDeclarationIdNo: string | number | null;
		/** 报关人姓名 */
		customsDeclarationName: string | number | null;
		/** 报关人证件类型 */
		customsDeclarationIdType: string | number | null;
		/** 订单备注 */
		remark: string | number | null;
		/** 订单完成时间 */
		finishTime: string | number | null;
		/** 支付时间 */
		payTime: string | number | null;
		/** 下单时间，也是创建时间 */
		createdTime: string | number | null;
		/** 物流发货时间 */
		deliveryTime: string | number | null;
		/** 自动确认收货的时间 */
		autoConfirmTime: string | number | null;
		/** 订单取消原因。0:无取消订单操作;1:系统后端超时自动取消;2:系统前端超时自动取消;3:不想要了;4:商品选错/多选;5:商品无货;6:地址信息填写错误;7:价格有点贵;8:买多了;9:其他; */
		cancelReason:
			| 0
			| 1
			| 2
			| 5
			| 6
			| 7
			| 8
			| 9
			| "0"
			| "1"
			| "2"
			| "5"
			| "6"
			| "7"
			| "8"
			| "9";
		/** 订单状态(0:未付款，1:已付款，2:待发货，3：部分发货， 5:待收货，6：已完成，7:已取消，8：支付异常，9：交易关闭) */
		orderStatus:
			| 0
			| 1
			| 2
			| 3
			| 5
			| 6
			| 7
			| 8
			| 9
			| 81
			| "0"
			| "1"
			| "2"
			| "3"
			| "5"
			| "6"
			| "7"
			| "8"
			| "9"
			| "81";
		/** 支付状态 （0:支付中 1:支付成功 2:支付失败 3:支付异常）*/
		paymentStatus: 0 | 1 | 2 | 3 | "0" | "1" | "2" | "3";
		/** 发货状态 (2:未发货 3:部分发货 5:全部发货 6:已收货 7:已取消)*/
		deliveryStatus: 2 | 3 | 5 | 6 | 7 | "2" | "3" | "5" | "6" | "7";
		/** 售后状态 (0:全部 1:审核中 2:已拒绝 3:退款中 4:退款成功 5:待退货 6:退回中 7:已入库 8:拒绝入库 9:协商关单 10:已取消 11:商家寄回 12:换货成功 13:已关闭)*/
		afterSalesStatus:
			| 0
			| 1
			| 2
			| 3
			| 4
			| 5
			| 6
			| 7
			| 8
			| 9
			| 10
			| 11
			| 12
			| 13
			| "0"
			| "1"
			| "2"
			| "3"
			| "4"
			| "5"
			| "6"
			| "7"
			| "8"
			| "9"
			| "10"
			| "11"
			| "12"
			| "13";
		/** 退货状态; 0:未售后;2:部分售后;3:全部售后;4:售后中 */
		withdrawStatus: 0 | 2 | 3 | 4 | "0" | "2" | "3" | "4";
		/** 是否预售 **/
		isPreSale?: 0 | 1 | "0" | "1";
		/** 组合商品 **/
		skuType?: 1 | 2 | "1" | "2";
		/** 评论状态(0:待评价，1:已评价) */
		commentStatus: 0 | 1 | "0" | "1";
		/** 是否开票(0:无需开票，1:需要开票) */
		invoiceStatus: 0 | 1 | "0" | "1";
		/** 商城报关状态;0:未报关;1:报关成功；2:报关失败 */
		mallCustomsDeclarationStatus: 0 | 1 | 2 | "0" | "1" | "2";
		/** 支付报关状态;0:未报关;1:报关成功；2:报关失败 */
		payCustomsDeclarationStatus: 0 | 1 | 2 | "0" | "1" | "2";
		/** 仓库报关状态;0:未报关;1:报关成功；2:报关失败 */
		warehouseCustomsDeclarationStatus: 0 | 1 | 2 | "0" | "1" | "2";

		/** 子订单集合 */
		subOrderTabRespList?: SubOrderTab[] | [];
		/** 子订单流水集合 */
		orderTxnTabRespList?: any[];
		/** 订单配送地址表 */
		receiverInfoResp?: ReceiverInfo;
		/** 快递信息 */
		logisticsRespList?: LogisticsResp[];
		/** 已退货商品信息*/
		returnGoodsList?: returnGoods[];
	}

	/** 子订单 */
	interface SubOrderTab {
		/** SKU ID */
		id?: string;
		/** 订单编号 */
		orderNo: string | null;
		/** SKU编码 */
		skuId: string | null;
		/** 商品名称 */
		skuName: string | null;
		/** 原价 */
		skuPrice: string | null;
		/** 数量 */
		itemSum: string | number | null;
		/** 支付价格 */
		paymentPrice: string | number | null;
		/** 	spu名 */
		spuName: string | null;
		/** sku主图 */
		sku_main_image_url: string | null;
		/** 子订单号 */
		subOrderNo: string | null;
		/** 子订单的价格 */
		subOrderPrice: string | number | null;
		/** 购买人ID */
		userId: string | null;
		/** sku稅率 */
		skuTaxRate: string | null;
		/** 是否赠品(0:否;1:是) */
		isGift: 0 | 1 | "0" | "1";
		/** 退货退款状态;0:未售后;2:部分售后;3:全部售后;4:售后中 */
		withdrawStatus: 0 | 2 | 3 | 4 | "0" | "2" | "3" | "4";
		/** 订单状态(0:未付款；1:待发货，2:待收货；3:已完成；4:已取消) */
		subOrderStatus: 0 | 1 | 2 | 3 | 4 | "0" | "1" | "2" | "3" | "4";
	}
	/** 快递信息 */
	interface LogisticsResp {
		/** 快递单号 */
		logisticsNo: string | null;
		/** 快递公司 */
		logisticsCompanyName: string | null;
		/** 快递公司 */
		logisticsCompanyCode: string | null;
		/** 备注 */
		remark: string | null;
		/** 物流轨迹信息 */
		trackInfo: string | number | null;
		/** 快递类型，1.保宏 2.快递100 */
		logisticsType: 1 | 2 | "1" | "2";
		/** 运单状态，0在途，1揽收，2疑难，3签收，4退签，5派件，6退回，7转投 */
		deliveryBillStatus:
			| 0
			| 1
			| 2
			| 3
			| 4
			| 5
			| 6
			| 7
			| "0"
			| "1"
			| "2"
			| "3"
			| "4"
			| "5"
			| "6"
			| "7";
		/** 订单商品SKU */
		skuList: any[];
	}
	interface returnGoods {
		/** 售后工单 */
		afterSalesNo?: string | null;
		/** sku名称*/
		skuName?: string | null;
		/** sku编码*/
		skuCode?: string | null;
		/** 规格名*/
		specName?: string | null;
		/** 品牌名称*/
		brandName?: string | null;
		/** 单价*/
		price?: number | null;
		/** 退货数量*/
		quantity?: number | null;
		/** 退货金额*/
		withdrawAmount?: number | null;
		/** 退运费*/
		withdrawLogisticsFee?: number | null;
		/** 运单状态*/
		logisticsStatus?: number | null;
		/** 未知属性*/
		[key: string]: any;
	}
	/** 订单配送地址 */
	interface ReceiverInfo {
		/** 规格组 ID */
		id?: string;
		/** 子订单号 */
		subOrderNo: string | null;
		/** 订单号 */
		orderNo: string | null;
		/** 快递单号 */
		losisticsNo: string | null;
		/** 发货时间 */
		deliveryTime: string | null;
		/** 快递公司 */
		losisticsCompanyName: string | null;
		/** 快快滴公司编码 */
		losisticsCompanyCode: string | null;
		/** 物流轨迹信息 */
		trackInfo: string | null;
	}
}
/** C端用户信息基础数据 */
declare namespace ApiCCustomInfos {
	interface CCustomInfos {
		/**  C端用户id */
		id?: string;
		/** 手机 */
		phone: string | number | null;
		/** 默认登录用户id */
		defaultLoginUserId: string | number | null;
		/** 账号ID */
		accountId: string | null;
		/** 登录账号 */
		account: string | null;
		/** 注册时间 */
		registerTime: string | number | null;
		/** 创建时间 */
		createdTime: string | number | null;
		/** 更新时间 */
		updatedTime: string | number | null;

		/** 用户名列表 */
		realNameList: string | string[] | null;
		/** 所属公司列表 */
		companyNameList: string | string[] | null;
		/** 密码状态;0:初始密码;1:密码已修改 */
		passwordStatus: 0 | 1 | "0" | "1";
		/** 账号状态;0:初始化，2:启用；3：禁用 4注销 5 废弃; */
		accountStatus: 0 | 2 | 3 | 4 | 5 | "0" | "2" | "3" | "4" | "5";
		/** 用户来源 0:未知;1:企业新增;2:在线注册 */
		registerChannel: 0 | 1 | 2 | "0" | "1" | "2";

		/** 福喜的guid */
		fuxiGuid: string | null;
		/** 账户下用户列表 */
		userList?: CUser[];
	}

	/** 账户下用户 */
	interface CUser {
		/** 用户 ID */
		id?: string;
		/** 福喜企业id */
		fuxiCompanyId: string | null;
		/** 福喜memberId */
		fuxiMemberId: string | null;
		/** wxopenid */
		wxopenid: string | null;
		/** 账号id */
		accountId: string | null;
		/** 公司id */
		companyId: string | number | null;
		/** 公司名 */
		companyName: string | number | null;
		/** 用户昵称 */
		nickName: string | null;
		/** 用户头像 */
		avatar: string | null;
		/** 邮箱 */
		email: string | null;
		/** 手机 */
		phone: string | number | null;
		/** 性别(0：男，1：女) */
		sex: 0 | 1 | "0" | "1";
		/** 出生日期;yyyy-MM-dd HH:mm */
		birthDate: string | null;
		/** 真实姓名 */
		realName: string | null;
	}
}
/** 积分模块基础数据 */
declare namespace ApiPonitInfos {
	/** 企业积分账户信息 */
	interface PointCompanyInfos {
		/** 积分账户id */
		id?: string;
		pointsRuleId?: string;
		ruleName?: string;
		/** 企业id */
		companyId: string | null;
		/** 企业名称 */
		name: string | null;
		/** 企业简称 */
		shortName: string | null;
		/** 可用积分 */
		availablePoints: string | null;
		/** 累计发放积分数 */
		accumulatedPoints: string | null;
	}

	/** 企业积分规则 */
	interface PointCompanyRules {
		/** 积分组id */
		id?: string;
		/** 积分规则id */
		pointsRuleId: string | null;
		/** 规则名称 */
		ruleName: string | null;
		/** 企业简称 */
		shortName: string | null;
		/** 可用积分 */
		availablePoints: string | null;
		/** 累计发放积分数 */
		accumulatedPoints: string | null;
		/** 累计发放积分次数 */
		accumulatedCount: string | null;
		/** 最近充值时间 */
		topUpTime: string | null;
	}
	/** 企业积分流水 */
	interface PointCompanyRecode {
		/** 流水记录id */
		id?: string;
		/** 积分id */
		pointsId: string | null;
		/** 充值时间 */
		topUpTime: string | null;
		/**流水类型 */
		transactionType: 2 | 3 | 4 | "2" | "3" | "4";
		/** 充值积分 */
		totalPoints: string | null;
		/** 该积分总消费积分 */
		allUsedPoints: string | null;
		/** 本次消费积分 */
		usedPoints: string | null;
		/** 剩余可用积分 */
		availablePoints: string | null;
		/** 过期积分 */
		expiredPoints: string | null;
		/** 生效时间 */
		effectiveTime: string | null;
		/** 过期时间 */
		expiredTime: string | null;
	}
}
