<template>
	<n-scrollbar class="flex-1-hidden">
		<n-menu :value="activeKey" :collapsed="app.siderCollapse" :collapsed-width="theme.sider.collapsedWidth"
			:collapsed-icon-size="22" :options="menus" :expanded-keys="expandedKeys" :render-label="renderMenuLabel"
			:indent="18" :inverted="!theme.darkMode && theme.sider.inverted" @update:value="handleUpdateMenu"
			@update:expanded-keys="handleUpdateExpandedKeys" />
	</n-scrollbar>
</template>

<script setup lang="ts">
import { computed, ref, watch, h } from 'vue';
import { useRoute } from 'vue-router';
import { NIcon } from 'naive-ui'
import type { MenuOption } from 'naive-ui';
import { useAppStore, useRouteStore, useThemeStore } from '@/store';
import { useRouterPush } from '@/composables';
import { getActiveKeyPathsOfMenus, translateMenuLabel, handleObject, localStg } from '@/utils';
import { CircleSolid } from '@vicons/carbon'
import { number } from 'echarts';
import axios from 'axios';
import { pullAt } from 'lodash'

defineOptions({ name: 'VerticalMenu' });

const route = useRoute();
const app = useAppStore();
const theme = useThemeStore();
const routeStore = useRouteStore();

const { routerPush } = useRouterPush();

const menus = computed(() => translateMenuLabel(routeStore.menus as App.GlobalMenuOption[]));
console.log('menustest', menus.value, routeStore.menus, localStg.get('userInfo'))
// initMentFn()
// axios.get(handleObject() + '/front/menus-goods.json').then(res =>{
//   console.log('handleObject', res)
//   if(res?.data?.list?.length > 0 ){
//     compareMenus(res.data.list, routeStore.menus)
//   }
// }).catch(err => {
//   console.log('err', err)
// })

// compareMenus(localStg.get('menus-goods')?.list, routeStore.menus)

// function compareMenus(list: any, menus: any) {
// 	list.forEach(item => {
// 		menus.forEach((menu, index) => {
// 			// 菜单名称相同
// 			if (item.name == menu.routeName) {
// 				// 判断 auth owner 是否一致
// 				if (item.auth && item.owner?.length > 0) {
// 					if (!item.owner?.includes(localStg.get('userInfo').userName)) {
// 						pullAt(menus, index)
// 					}
// 				}
// 				// 存在 item.children 递归调用
// 				if (item.children && item.children.length > 0) {
// 					compareMenus(item.children, menus[index].children)
// 				}
// 			}
// 		})
// 	})
// }

const activeKey = computed(() => (route.meta?.activeMenu ? route.meta.activeMenu : route.name) as string);
const expandedKeys = ref<string[]>([]);

function handleUpdateMenu(_key: string, item: MenuOption) {
	const menuItem = item as App.GlobalMenuOption;
	routerPush(menuItem.routePath);
}

function handleUpdateExpandedKeys(keys: string[]) {
	expandedKeys.value = keys;
}

function renderMenuLabel(option: MenuOption) {
	if ((option.key == 'paypal-settlement' || option.key == 'paypal-settlement_statement') && option.showCircle) {
		return h(
			'div',
			null,
			[
				h('span', { style: { 'margin-right': '10px' } }, option.label as string),
				h(NIcon, { color: 'red' }, { default: () => h(CircleSolid) })
			]

		)
	}
	return option.label as string
}

watch(
	() => route.name,
	() => {
		expandedKeys.value = getActiveKeyPathsOfMenus(activeKey.value, menus.value);
	},
	{ immediate: true }
);
</script>

<style scoped></style>
