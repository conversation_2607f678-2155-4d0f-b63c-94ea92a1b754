interface UserModel extends Auth.UserInfo {
  token: string;
  refreshToken: string;
  password: string;
}

export const userModel: UserModel[] = [
	{
		token: "__TOKEN_FYZ__",
		refreshToken: "__REFRESH_TOKEN_FYZ__",
		userId: "0",
		userName: "Fyz",
		userRole: "super",
		password: "fyz123",
	},
	{
		token: "__TOKEN_SUPER__",
		refreshToken: "__REFRESH_TOKEN_SUPER__",
		userId: "1",
		userName: "Super",
		userRole: "super",
		password: "SuperSuper123",
	},
	{
		token: "__TOKEN_ADMIN__",
		refreshToken: "__REFRESH_TOKEN_ADMIN__",
		userId: "2",
		userName: "Admin",
		userRole: "admin",
		password: "AdminAdmin123",
	},
	{
		token: "__TOKEN_USER01__",
		refreshToken: "__REFRESH_TOKEN_USER01__",
		userId: "3",
		userName: "User01",
		userRole: "user",
		password: "UserUser01123",
	},
];
