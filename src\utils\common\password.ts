const PWDCHARS = '~!@#$%^&*()_+{}":?;,';
/**
 * 根据传入密码验证 密码安全等级
 * @param pwd - 密码字符串
 * @returns {0,1,2,3} 0:无效 1，低 2：中 3：高
 */
export function checkPwdStrong(pwd: string) {
	let lv = 0;
	if(!pwd){
		return lv;
	}
	if (pwd.match(/[a-z]/g)) {
		lv++;
	}
	if (pwd.match(/[A-Z]/g)) {
		lv++;
	}
	if (pwd.match(/[0-9]/g)) {
		lv++;
	}
	if (pwd.match(/[~!@#$%^&*()_+{}":?;,.<>`=|\[\]-]/g)) {
		lv++;
	}
	if (pwd.length < 8 || pwd.length > 20) {
		lv = 0;
	}
	if (lv > 3) {
		lv = 3;
	}
	// console.log('iv', lv);
	return lv;
}
/**
 * 根据传入密码验证 密码安全等级
 * @param pwd - 密码字符串
 * @returns {0,1,2,3} 0:无效 1，低 2：中 3：高
 */
export function checkPwdStrong_old(pwd: string) {
	let lv = 0;
	if (pwd.match(/[a-z]/g)) {
		lv++;
	}
	if (pwd.match(/[0-9]/g)) {
		lv++;
	}
	if (pwd.match(/[A-Z]/g)) {
		lv++;
	}
	if (pwd.match(/[~!@#$%^&*()_+{}":?;,]/g)) {
		lv++;
	}
	if (pwd.length < 6 || pwd.length > 20) {
		lv = 0;
	}
	if (lv > 3) {
		lv = 3;
	}
	console.log(lv);
	return lv;
}

/**
 * 随机生成密码函数
 * @param {number} length 需要大于等于12，默认12位
 * @returns {string}
 */
export function getStrongPwd(length = 12) {
	// getStrongPwd = function (length = 12) {
	// 对象的所有函数名，我们将使用它们来创建密码的随机字母
	const randomFunc = [
		getRandomLower,
		getRandomUpper,
		getRandomNumber,
		getRandomSymbol,
	];
	// 生成器函数
	// 所有负责返回一个随机值的函数，我们将使用它来创建密码。
	function getRandomLower() {
		return String.fromCharCode(Math.floor(Math.random() * 26) + 97);
	}
	function getRandomUpper() {
		return String.fromCharCode(Math.floor(Math.random() * 26) + 65);
	}
	function getRandomNumber() {
		return String.fromCharCode(Math.floor(Math.random() * 10) + 48);
	}
	function getRandomSymbol() {
		return PWDCHARS[Math.floor(Math.random() * PWDCHARS.length)];
	}
	if (length < 12) {
		length = 12;
	}
	let generatedPassword = "";
	var count = 0;
	while (count < length) {
		var t = count % 4;
		generatedPassword += randomFunc[t]();
		count++;
	}
	return generatedPassword
		.split("")
		.sort((a, b) => (Math.random() * 100 > 50 ? -1 : 1))
		.join("");
}
