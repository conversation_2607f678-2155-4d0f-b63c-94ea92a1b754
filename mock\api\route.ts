import type { <PERSON>ck<PERSON>ethod } from "vite-plugin-mock";
import { routeModel, userModel } from "../model";

const apis: MockMethod[] = [
	{
		url: "/mock/getUserRoutes",
		method: "post",
		response: (options: Service.MockOption): Service.MockServiceResult => {
			const { userId = undefined } = options.body;

			const routeHomeName: AuthRoute.LastDegreeRouteKey = "supplier_manage";

			const role =
				userModel.find((item) => item.userId === userId)?.userRole || "user";
			console.log("=========================mock role", role);
			const filterRoutes = routeModel[role];

			return {
				code: 0,
				message: "ok",
				data: {
					routes: filterRoutes,
					home: routeHomeName,
				},
			};
		},
	},
];

export default apis;
