<template>
	<div>
		<n-card :bordered="false" class="rounded-16px shadow-sm">
			<n-form ref="formRef" :model="searchCond" label-placement="top" label-width="auto" size="medium"
				:show-label="true">
				<n-grid :cols="24" :x-gap="24">
					<n-form-item-gi :span="8" path="blNo" label="B/L No.">
						<n-input v-model:value="searchCond.blNo" placeholder="B/L No." clearable />
					</n-form-item-gi>
					<n-form-item-gi :span="8" path="containerNo" label="Continer No.">
						<n-input v-model:value="searchCond.containerNo" placeholder="Continer No." clearable />
					</n-form-item-gi>

					<n-form-item-gi :span="8">
						<n-space>
							<n-button type="primary" @click="pagination.page = 1; getTableData()">
								Search List
							</n-button>
							<n-button type="default" @click="resetTable">
								Reset
							</n-button>
						</n-space>
					</n-form-item-gi>
				</n-grid>
			</n-form>
			<n-space class="pb-12px" justify="space-between">
				<n-space>
					<n-button type="success" @click="getTableData">
						<icon-mdi-refresh class="mr-4px text-16px" :class="{ 'animate-spin': loading }" />
						Refresh
					</n-button>
					<n-button type="primary" style="margin-left: 16px;" @click="batchReview">
						Add Task
					</n-button>
					<n-button type="primary" style="margin-left: 16px;" @click="autoReviewGoods">
						Batch Import
					</n-button>
					<n-button type="primary" style="margin-left: 16px;" @click="batchDelete"
						:disabled="selectRowskeys.length < 2">
						Batch Delete
					</n-button>
					<n-button type="primary" style="margin-left: 16px;" :disabled="selectRowskeys.length < 2"
						@click="batchArchive">
						Batch Archiving
					</n-button>
				</n-space>
				<!-- <n-space align="center" :size="18">
				<column-setting v-model:columns="columns" />
			</n-space> -->
			</n-space>
		</n-card>
		<n-space :vertical="true" :size="16">

			<n-card :bordered="false" class="h-full rounded-16px shadow-sm">
				<n-data-table remote v-model:checked-row-keys="selectRowskeys" :columns="columns" :data="tableData"
					:single-line="false" :loading="loading" :scroll-x="1500" :row-key="rowKey"
					@update:checked-row-keys="handleCheck" :pagination="pagination" @update:page="getTableData"
					@update-page-size="getTableData" striped />
			</n-card>
			<n-modal v-model:show="newBuildModal" class="custom-card" preset="card" :style="bodyStyle"
				:title="isTitleAdd ? 'Add' : 'Edit'">
				<n-form style="margin-bottom:10px ;" ref="batchReviewformRef" :model="batchReviewModel" :rules="rules"
					size="medium" label-placement="left" label-width="125px">
					<n-form-item label="shipper" path="shipper">
						<!-- <n-input v-model:value="batchReviewModel.shipper" maxlength="100" placeholder="shipper" /> -->
						<n-select v-model:value="batchReviewModel.shipper" :options="shipperOptions" placeholder="Please choose" />

					</n-form-item>
					<n-form-item label="B/L No." path="blNo">
						<n-input v-model:value="batchReviewModel.blNo" maxlength="100" placeholder="blNo" />
					</n-form-item>
					<n-form-item label="Continer No." path="containerNo">
						<n-input v-model:value="batchReviewModel.containerNo" maxlength="100" placeholder="containerNo" />
					</n-form-item>
				</n-form>
				<n-space justify="center">
					<n-button type="primary" size="large" @click="handleValidateButtonClick(3)">
						Save
					</n-button>
					<n-button size="large" @click="handleValidateButtonClick(4)">
						Back
					</n-button>
				</n-space>
			</n-modal>

			<n-modal v-model:show="showUploadModal" :mask-closable="false" class="custom-card" preset="card"
				:style="bodyStyle2" title="Batch Import" size="huge" :bordered="false" :segmented="segmented"
				@after-leave="leaveUpload">
				<n-form ref="formRefAuto" :model="batchUploadModel" :rules="rulesUpload" size="medium" label-align="left"
					label-placement="left" label-width="auto">
					<n-grid :cols="24" :x-gap="24">
						<n-form-item-gi :span="20" label="Download Template:">
							<n-button quaternary type="info" @click="downloadTemplate">
								Template File
							</n-button>
						</n-form-item-gi>
						<n-form-item-gi :span="20" label="File Upload:" path="fileName">
							<!-- 点击上传 -->
							<n-upload class="upload_icon" accept=".xls,.xlsx" v-show="batchUploadModel.files.length < 1"
								:custom-request="customRequest" @before-upload="beforeUpload" @change="updateImgList"
								:show-file-list="false">
								<n-button size="large"> Upload</n-button>
							</n-upload>
							<span v-if="batchUploadModel.files[0]?.name"
								class="lh-34px mr-6px">{{ batchUploadModel.files[0]?.name }}</span>
							<n-upload v-if="batchUploadModel.files[0]?.name" class="upload_icon w-40px lh-34px" accept=".xls,.xlsx"
								:custom-request="(v) => { customRequest({ ...v, flag: 1 }) }" @before-upload="beforeUpload"
								@change="updateImgList" :show-file-list="false">
								<span class="text-blue">重选</span>
							</n-upload>
						</n-form-item-gi>
						<!-- <n-form-item-gi :span="20" label="任务名称:" path="taskName">
						<n-input v-model:value="batchUploadModel.taskName" maxlength="50"></n-input>
					</n-form-item-gi> -->

					</n-grid>
					<div>
						<div style="color: #666666;">Attachments:</div>
						<div style="color: rgb(194, 194, 194);">The maximum size for a single attachment is 20M. Supported formats
							include: pdf,
							doc, xls, zip, rar, jpg, mp4, txt.</div>

					</div>


				</n-form>
				<template #footer>
					<div style="display: flex; justify-content: flex-end;">
						<n-button type="primary" style="margin-right: 15px;" @click="uploadBatchAutoReview"
							:disabled="batchUploadModel.files.length < 1" :loading="btnLoading">Analyze</n-button>
						<n-button @click='leaveUpload'>Back</n-button>
					</div>
				</template>
			</n-modal>
			<n-modal v-model:show="showUploadSucessModal" :mask-closable="false" class="custom-card" preset="card"
				:style="bodyStyle2" title="" size="huge" :bordered="false" :segmented="segmented"
				@after-leave="leaveUploadSucess">

				<div>
					文件上传成功,上传结果请到
					<n-button quaternary type="info" ghost @click="goToTaskManagement">
						任务管理
					</n-button>
					菜单查看
				</div>
				<template #footer>
					<div style="display: flex; justify-content: flex-end;">
						<n-button type="primary" style="margin-right: 15px;" @click="leaveUploadSucess">确定</n-button>
						<n-button @click="leaveUploadSucess">取消</n-button>
					</div>
				</template>
			</n-modal>

		</n-space>
	</div>
</template>

<script setup lang="tsx">
import { reactive, ref, onMounted, onActivated, watch } from 'vue';
import type { Ref } from 'vue';
import { cloneDeep, uniq, values, remove, divide } from 'lodash-es';
import { NButton, NPopconfirm, NSpace, NTag, FormInst } from 'naive-ui';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { fetchTrackList, fetchTrackAdd, fetchTrackEdit, fetchDeleteBatchIds, fetchArchiveBatch } from '@/service';
import { useBoolean, useLoading } from '@/hooks';
import { formatDate, MathToFixedStr, getLabelByOptions, createRequiredFormRule, renameDownload } from '@/utils';
import { useIconRender } from '@/composables';
const { iconRender } = useIconRender();

const { loading, startLoading, endLoading } = useLoading(true);
const { loading: btnLoading, startLoading: btnStartLoading, endLoading: btnEndLoading } = useLoading(false);

import { useRouterPush } from '@/composables';
import { routeName } from '@/router';
const { routerPush } = useRouterPush();

const formRefAuto = ref<HTMLElement & FormInst>();

const fileUrl = ref('')

const showUploadModal = ref(false)

const showUploadSucessModal = ref(false)

const bodyStyle2 = {
	width: "650px",
	minWidth: 0,
}
const checkedRowKeysItem = ref<Array<string | number>>([])

const segmented = reactive(
	{
		content: 'soft',
		footer: 'soft'
	}
)

interface batchUploadModel {
	taskName: any
}
const shipperOptions = [
	{
		label: 'COSCO',
		value: 'COSCO',
	},
	{
		label: 'YML',
		value: 'YML',
	},
	// {
	// 	label:'HMM',
	// 	value:'HMM',
	// },
	{
		label:'TSL',
		value:'TSL',
	},
	{
		label:'ONE',
		value:'ONE',
	},
	{
		label:'OOCL',
		value:'OOCL',
	},
	{
		label: 'KMTC',
		value: 'KMTC',
	},
	{
		label: 'MSC',
		value: 'MSC',
	},
	{
		label: 'MSK',
		value: 'MSK',
	},
	{
		label: 'EMC',
		value: 'EMC',
	},
	{
		label: 'PIL',
		value: 'PIL',
	},

]


const batchUploadModel = reactive({
	files: [],
	fileName: '',
	taskName: null
})

const rulesUpload = {
	fileName: createRequiredFormRule('请上传文件'),
	taskName: createRequiredFormRule('请输入任务名称'),
}

// const files = ref<UploadFileInfo[]>([])

const customRequest = async ({
	file,
	action,
	onFinish,
	onError,
	flag
}: UploadCustomRequestOptions) => {
	try {
		if (flag == 1) {
			batchUploadModel.files = []
		}
		// batchUploadModel.files.push(file)
		// batchUploadModel.fileName = file.name
		const { code, data } = await autoReviewTemplateUpload({ file: file.file })
		if (code == 0) {
			//
			fileUrl.value = data?.dataId
			batchUploadModel.files.push(file)
			batchUploadModel.fileName = file.name
			onFinish()
		} else {
			fileUrl.value = ''
			batchUploadModel.files = []
			batchUploadModel.fileName = ''
			onError()
		}

	} catch (e) {
		console.error(e)
		onError()
	}


}

const beforeUpload = async ({ file, fileList }: { file: UploadFileInfo, fileList: Array<UploadFileInfo>, event?: Event }) => {
	if (file.file.size > 2 * 1024 * 1024) {
		window.$message?.error('上传失败，文件大小超出限制，请重新上传!');
		return false;
	}
	return true
}

const updateImgList = ({ file, fileList, event }: { file: UploadFileInfo, fileList: Array<UploadFileInfo>, event?: Event }) => {
	if (file.status == 'removed') {
		// 删除图片
		files.value.forEach((f, i) => {
			if (f.id == file.id) {
				files.value.splice(i, 1);
			}
		})
	}
}




const supplierSpuIdList = ref([])
const selectRowskeys = ref([])
const selectRowsId = ref([])
const batchReviewDisable = ref(true)
function rowKey(row: any) {
	return row.id
}

function handleCheck(rowKeys: any, rows: object[], meta: { row: object | undefined, action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }) {
	console.log('handleCheck1', rowKeys, meta, selectRowsId.value, selectRowskeys.value)
	let tempId = meta.row?.id
	if (meta.action == 'check') {
		selectRowsId.value.push(tempId + '')
	} else if (meta.action == 'uncheck') {
		remove(selectRowsId.value, _ => _ == tempId);
	} else if (meta.action == 'checkAll') {
		selectRowsId.value = []
		rowKeys.forEach(key => {
			selectRowsId.value.push(key.split('_')[0])
		})
		selectRowsId.value = uniq(selectRowsId.value)
	} else {
		selectRowsId.value = []
	}
	if (selectRowsId?.value.length > 0) {
		batchReviewDisable.value = false
	} else {
		batchReviewDisable.value = true
		rowKeys = []
	}
	selectRowskeys.value = rowKeys
	console.log('handleCheck2', rowKeys, meta, selectRowsId.value, selectRowskeys.value)
}

const bodyStyle = {
	width: "600px",
	minWidth: 0,
}
const isTitleAdd = ref(true)
const newBuildModal = ref(false)
function batchReview(item: any) {
	newBuildModal.value = true
	console.log('selectRowsId.value', selectRowsId.value)
	supplierSpuIdList.value = selectRowsId.value
	if (item.shipper) {
		isTitleAdd.value = false
		batchReviewModel.blNo = item.blNo
		batchReviewModel.shipper = item.shipper
		batchReviewModel.containerNo = item.containerNo
		batchReviewModel.id = item.id
	} else {
		isTitleAdd.value = true
	}
}

const batchReviewModel = reactive({
	shipper: null,
	blNo: '',
	containerNo: '',
	id: ''
})
const rules = {
	shipper: createRequiredFormRule('Please choose'),
	// blNo: createRequiredFormRule('B/L No. can not be empty'),
	// containerNo: createRequiredFormRule('Continer No. can not be empty'),

}
const batchReviewformRef = ref<HTMLElement & FormInst>();


const resetTrackAddModal = () => {
	batchReviewModel.shipper = null
	batchReviewModel.blNo = ''
	batchReviewModel.containerNo = ''
	batchReviewModel.id = ''
	newBuildModal.value = false

}
watch(
	newBuildModal,
	(newValue) => {
		if (!newValue) {
			resetTrackAddModal()
		}
	}
)
const handleValidateButtonClick = (reviewStatus: 3 | 4) => {
	if (reviewStatus == 3) {
		let params = {
			...batchReviewModel
		}
		batchReviewformRef.value?.validate(async (errors) => {
			if (errors) {
				console.log(errors)
			} else {
				if (isTitleAdd.value) {
					delete params.id
					const { code, data } = await fetchTrackAdd(params);
					if (code == 200) {
						resetTrackAddModal()
						window.$message?.success('success');
						getTableData()
					} else {
						window.$message?.error('error');
					}
				} else {

					const { code, data } = await fetchTrackEdit(params);
					if (code == 200) {
						resetTrackAddModal()
						window.$message?.success('success');
						getTableData()

					} else {
						window.$message?.error('error');
					}
				}


			}
		})
	} else {
		resetTrackAddModal()

		newBuildModal.value = false
	}
}

const searchCond = reactive({
	blNo: "",
	containerNo: '',
});

const columns = ref([
	{
		type: 'selection',
		align: 'center',
		fixed: 'left',
		// disabled(row: RowData) {
		// 	return row.reviewStatus != 2
		// }
	},
	{
		key: 'index',
		title: '#',
		width: '50px',
		align: 'center',
		fixed: 'left',
	},
	{
		key: 'shipper',
		title: 'Shipper',
		width: '180px',
		align: 'center',
		fixed: 'left',
	},
	{
		key: 'blNo',
		// sorter: true,
		// sortOrder: false,
		title: 'B/L No.',
		minWidth: '120px',
		align: 'center',
	},
	{
		key: 'containerNo',
		title: 'Container No.',
		width: '150px',
		align: 'center'
	},
	{
		key: 'etaDate',
		title: 'ETA',
		minWidth: '200px',
		align: 'center',
		render: row => (
			<div style="display:flex;justify-content:center">
				{row.etaStatus == 0 ? <NButton style='color:#cccccc;font-size:24px;margin-right:10px' size={'medium'} text >
					{iconRender({ icon: 'qlementine-icons:clock-16' })}
				</NButton> : row.etaStatus == 1 ? <NButton style='color:green;font-size:24px;margin-right:10px' size={'medium'} text >
					{iconRender({ icon: 'qlementine-icons:success-16' })}
				</NButton> : row.etaStatus == 2 ? <NButton style='color:rgb(164,0,0);font-size:24px' size={'medium'} text >
					{iconRender({ icon: 'mi:circle-error' })}
				</NButton> : row.etaStatus == 3 ? <div style="display:flex;justify-content:center"><NButton style='color:orange;font-size:24px;margin-right:10px' size={'medium'} text >
					{iconRender({ icon: 'qlementine-icons:warning-16' })}
				</NButton><p style='color:orange'>{'To Be Confirmed'}</p></div> : row.etaStatus == 4 ? <div style="display:flex;justify-content:center"><NButton style='color:orange;font-size:24px;margin-right:10px' size={'medium'} text >
					{iconRender({ icon: 'qlementine-icons:warning-16' })}
				</NButton><p style='color:orange'>{'To Be Adviced'}</p></div> : null}

				{row.etaDate ? <p style='margin-left:10px'>{row.etaDate}</p> : ''}
			</div>
		)
	},

	{
		key: 'creator',
		title: 'Creator',
		width: '90px',
		align: 'center',
	},

	{
		key: 'createTime',
		title: 'Create Date',
		width: '180px',
		align: 'center',
		fixed: 'right',
	},
	{
		key: 'updateBy',
		title: 'Last Update',
		// width: '120px',
		align: 'center',
		fixed: 'right',
	},

	{
		key: 'actions',
		title: 'Action',
		width: '220px',
		align: 'center',
		fixed: 'right',
		rowSpan: (row: any) => { return row.rowSpan },
		render: row => {
			return (
				<div style="display:flex;justify-content:space-evenly">

					<NButton style='color:rgb(0,157,255);font-size:24px' size={'medium'} text onClick={() => getTableData()}>
						{iconRender({ icon: 'streamline:magnifying-glass' })}
					</NButton>
					<NButton onClick={() => batchReview(row)} style='color:rgb(0,157,255);font-size:24px' size={'medium'} text >
						{iconRender({ icon: 'material-symbols:contract-edit-outline' })}
					</NButton>
					<NButton style='color:rgb(0,157,255);font-size:24px' size={'medium'} text onClick={() => batchArchive(row)}>
						{iconRender({ icon: 'mingcute:briefcase-line' })}
					</NButton>
					<NButton style='color:red;font-size:24px' size={'medium'} text onClick={() => batchDelete(row)}  >
						{iconRender({ icon: 'ri:delete-bin-6-line' })}
					</NButton>
				</div>
			)
		}
	},
]);
const tableTotal = ref<number>(0)
const tableData = ref([]);
async function getTableData() {
	startLoading();
	selectRowskeys.value = [];//清空批量选择的行
	selectRowsId.value = []; //清空批量选择的行
	const params = {
		size: pagination.pageSize,
		current: pagination.page,
		...searchCond
	}
	const { code, data } = await fetchTrackList(params);
	endLoading();
	if (code == 200) {
		tableTotal.value = data?.total || 0;
		pagination.page = data?.current;
		pagination.pageCount = Math.ceil(data?.total / pagination?.pageSize);
		tableData.value = []
		data?.list?.forEach((item, index) => {
			item.index = index + 1

		});
		tableData.value = data?.list;
	}
	console.log(tableData.value);
}
function resetTable() {
	searchCond.blNo = ''
	searchCond.containerNo = ''
	pagination.page = 1

	newBuildModal.value = false
	supplierSpuIdList.value = []
	selectRowsId.value = []
	selectRowskeys.value = []
	getTableData();
}

const pagination: PaginationProps = reactive({
	page: 1,
	pageSize: 10,
	pageCount: 0,
	showSizePicker: true,
	showQuickJumper: true,
	pageSizes: [10, 20, 50, 100],
	prefix: () => <p>Total:{tableTotal.value} </p>,
	// prefix: () => <p>Total:{tableTotal.value} | Pending:{tableTotal.value} | Success:{tableTotal.value}  Failed: :{tableTotal.value} </p>,
	onChange: (page: number) => {
		pagination.page = page;
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.pageSize = pageSize;
		pagination.page = 1;
	},
	goto: () => <div>to</div>
});

async function init() {
	getTableData();
}


function leaveUpload() {

	batchUploadModel.files = [];
	batchUploadModel.fileName = '';
	fileUrl.value = ''
	batchUploadModel.taskName = null;

	showUploadModal.value = false
}



async function downloadTemplate() {
	const params = {
		type: 13
	}
	const { code, data } = await autoReviewTemplate(params)
	if (code == 0) {
		//const fileName = data?.name + formatDate(new Date()) + '.xlsx';
		const fileName = "自动审核商品模板" + '.xlsx';
		renameDownload(data?.templateUrl, fileName)
	}

}

function uploadBatchAutoReview() {
	formRefAuto.value?.validate(async (errors) => {
		if (!errors) {
			const params = {
				fileUrl: fileUrl.value,
				taskName: batchUploadModel.taskName?.trim()
			}
			btnStartLoading()
			const { code, data } = await autoExcelBatchReview(params)
			btnEndLoading()
			if (code == 0) {
				if (data?.pass) {
					// window.$message?.success('自动审批成功')
					leaveUpload()
					showUploadSucessModal.value = true
				} else {
					// window.$message?.success('自动审批成功')
					showUploadSucessModal.value = false
				}

			}
		}

	})
}

function leaveUploadSucess() {
	batchUploadModel.files = [];
	batchUploadModel.fileName = '';
	fileUrl.value = ''
	batchUploadModel.taskName = null;
	showUploadModal.value = false
	showUploadSucessModal.value = false
	getTableData();
}

function goToTaskManagement() {
	leaveUploadSucess()
	routerPush({ name: routeName('task-management_import') });
}

function autoReviewGoods() {
	showUploadModal.value = true
}

const batchDelete = async (row: any) => {
	let params
	if (row.id) {
		params = [row.id]
	} else {
		params = selectRowskeys.value
	}
	const { code, data } = await fetchDeleteBatchIds(params);
	if (code == 200) {
		window.$message?.success('success');
		getTableData()

	} else {
		window.$message?.error('error');
	}
}
const batchArchive = async (row: any) => {
	let params
	if (row.id) {
		params = [row.id]
	} else {
		params = selectRowskeys.value
	}
	const { code, data } = await fetchArchiveBatch(params);
	if (code == 200) {
		window.$message?.success('success');
		getTableData()

	} else {
		window.$message?.error('error');
	}
}
onActivated(() => {
	init()
})
</script>

<style scoped>
.spucode {
	word-break: break-all;
	word-wrap: normal
}
</style>
