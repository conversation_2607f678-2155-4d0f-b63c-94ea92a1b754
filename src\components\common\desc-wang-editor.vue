<template>
	<div class="form-group-item">
		<div style="border: 1px solid #ccc">
			<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" mode="simple" />
			<Editor style="height: 500px; overflow-y: hidden;" v-model="editorHtml" :defaultConfig="editorConfig" mode="default"
				@onCreated="handleCreated" />
		</div>
		<div class="tips-box">
			<p>* 请尽量以图片的形式替代无规则样式的文案</p>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, shallowRef, onBeforeUnmount, watch} from 'vue'
import { fetchUpload } from '@/service';

import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
type InsertImgFnType = (url: string, alt: string, href: string) => void
type InsertVideoFnType = (url: string, poster: string) => void

interface Props {
	valueHtml: string;
	readOnly:boolean;
}
interface Emits {
	(e: 'update:valueHtml', valueHtml: string): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const editorRef = shallowRef()
const toolbarConfig = {}
const editorConfig = {
	placeholder: '请输入内容...',
	readOnly: props.readOnly,
	MENU_CONF: {
		'uploadImage': {
			// 单个文件的最大体积限制，默认为 2M
			maxFileSize: 10 * 1024 * 1024, // 10M
			// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['image/*'],
			// 自定义上传
			async customUpload(file: File, insertFn: InsertImgFnType) {  // TS 语法
				// async customUpload(file, insertFn) {                   // JS 语法
				// file 即选中的文件
				console.log(file)
				let { code, data } = await fetchUpload({ file: file })
				if (code == 0) {
					// 自己实现上传，并得到图片 url alt href
					// 最后插入图片
					insertFn(data.tempUrl, file.name, data.dataId)
				}
				// console.log(res,file)
			}
		},
		'uploadVideo': {
			// 单个文件的最大体积限制，默认为 2M
			maxFileSize: 10 * 1024 * 1024, // 10M
			// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['video/*'],
			// 自定义上传
			async customUpload(file: File, insertFn: InsertVideoFnType) {  // TS 语法
				// async customUpload(file, insertFn) {                   // JS 语法
				// file 即选中的文件
				console.log(file)
				let { code, data } = await fetchUpload({ file: file })
				if (code == 0) {
					// 自己实现上传，并得到图片 url alt href
					// 最后插入图片
					insertFn(data.tempUrl, file.poster || '')
				}
				// console.log(res,file)
			}
		}
	}
}
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
	const editor = editorRef.value
	if (editor == null) return
	editor.destroy()
})
const handleCreated = (editor) => {
	editorRef.value = editor // 记录 editor 实例，重要！
}
const editorHtml = ref('')
watch(
	() => props.valueHtml,
	() => {
		console.log('props.valueHtml',props, editorHtml.value)
		editorHtml.value = props.valueHtml || ''
		emit('update:valueHtml', editorHtml.value);
	})
watch(
	() => editorHtml.value,
	() => {
		console.log('editorHtml.value', props, editorHtml.value)
		emit('update:valueHtml', editorHtml.value);
	})
</script>
<style scoped lang="scss"></style>
