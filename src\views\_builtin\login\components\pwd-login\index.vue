<template>
	<n-form ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keydown="autoSubmit">
		<n-form-item path="userName">
			<n-input v-model:value="model.userName" placeholder="请输入用户名" />
		</n-form-item>
		<n-form-item path="password">
			<n-input v-model:value="model.password" type="password" show-password-on="click" placeholder="请输入密码" />
		</n-form-item>
		<n-form-item path="imgCode">
			<n-input v-model:value="model.imgCode" placeholder="验证码,点击图片刷新" />
			<div class="pl-8px">
				<!-- <image-verify v-model:code="imgCode" /> -->
				<img @click="handleRefreshImgCode" width="152" height="40" class="cursor-pointer" style="display: block;" :src="urlencoded"/>
			</div>
		</n-form-item>
		<n-space :vertical="true" :size="24">
			<div class="flex-y-center justify-between">
				<n-checkbox v-model:checked="rememberMe">记住我</n-checkbox>
				<!-- <n-button :text="true" @click="toLoginModule('reset-pwd')">忘记密码？</n-button> -->
			</div>
			<n-button type="primary" size="large" :block="true" :round="true" :loading="auth.loginLoading"
				@click="handleSubmit">
				登录
			</n-button>
			<!-- <div class="flex-y-center justify-between">
        <n-button class="flex-1" :block="true" @click="toLoginModule('code-login')">
          {{ loginModuleLabels['code-login'] }}
        </n-button>
        <div class="w-12px"></div>
        <n-button class="flex-1" :block="true" @click="toLoginModule('register')">
          {{ loginModuleLabels.register }}
        </n-button>
      </div> -->
		</n-space>
		<!-- <other-account @login="handleLoginOtherAccount" /> -->
	</n-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { FormInst, FormRules,FormItemRule } from 'naive-ui';
// import { loginModuleLabels } from '@/constants';
import { localStg,createRequiredFormRule } from "@/utils";
import { useAuthStore } from '@/store';
import { useRouterPush } from '@/composables';
import { formRules, getImgCodeRule } from '@/utils';
import { fetchImgCaptcha } from "@/service";
const urlencoded = ref('')
// import { OtherAccount } from './components';
const handleGeCaptcha = async()=>{
	let { code, data } = await fetchImgCaptcha({
		accountType: 3
	})
      if (code == 0) {
          urlencoded.value ='data:image/png;base64,'+data
      }
}
handleGeCaptcha()
const handleRefreshImgCode = ()=>{
	handleGeCaptcha()
}
const auth = useAuthStore();
const { login } = useAuthStore();
const { toLoginModule } = useRouterPush();

const formRef = ref<HTMLElement & FormInst>();

let rememberMeData = JSON.parse(localStg.get("rememberMe") || null);
console.log('rememberMeData', rememberMeData)
const model = reactive({
	// userName: 'caspar',
	// password: '123456',
	userName: '',
	password: '',
	imgCode: ''
});
const rememberMe = ref(false);
if (rememberMeData) {
	model.userName = rememberMeData.userName
	model.password = rememberMeData.password
	rememberMe.value = true;
}

const rules: FormRules = {
	// password: formRules.pwd,
	imgCode:[
		createRequiredFormRule('请输入验证码'),
		{
			validator(rule: FormItemRule, value: string) {
				if (value.length && value.trim()=='') {
					return false;
				}
				if (value.length!=4 && value.length) {
					return false
				}
				return true;
			},
			trigger: ['blur', 'change'],
			message: '请输入正确的验证码'
		}
	],
};

async function handleSubmit() {
	// todo 登录校验
	await formRef.value?.validate();

	const { userName, password } = model;

	login(userName, password, rememberMe.value,model.imgCode,handleGeCaptcha);
}

async function autoSubmit(event: KeyboardEvent) {
	console.log('autoSubmit', event)
	if (event.key == 'Enter') {
		handleSubmit()
	}
}
// function handleLoginOtherAccount(param: { userName: string; password: string }) {
//   const { userName, password } = param;
//   login(userName, password);
// }
</script>

<style scoped></style>
