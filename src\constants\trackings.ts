const tracking_urls: Record<string, string> = {
    "CMA": "https://www.cma-cgm.com/ebusiness/tracking",
    "COSCO": "https://elines.coscoshipping.com/scct/public/ct/containers",
    "EMC": "https://ct.shipmentlink.com/servlet/TDB1_CargoTracking.do",
    "HMM": "https://www.hmm21.com/e-service/general/trackNTrace/TrackNTrace.do",
    "HPL": "https://www.hapag-lloyd.cn/en/online-business/track/track-by-container-solution.html?container=",
    "IAL": "https://www.interasia.cc/Service/Form?servicetype=0",
    "KMTC": "https://www.ekmtc.com/index.html#/cargo-tracking",
    "MSC": "https://www.msccargo.cn/",
    "MSK": "https://www.maersk.com.cn/",
    "ONE": "https://ecomm.one-line.com/ecom/CUP_HOM_3301GS.do",
    "OOCL": "https://www.oocl.com/eng/ourservices/eservices/cargotracking/pages/cargotracking.aspx",
    "PIL": "https://www.pilship.com/digital-solutions/?tab=customer&id=track-trace&label=containerTandT&module=TrackContStatus&refNo=",
    "RCL": "https://eservice.rclgroup.com/CargoTracking",
    "TSL": "https://www.tslines.com/en/tracking",
    "WHL": "https://cn.wanhai.com/cec/#/cargotracking",
    "YML": "https://www.yangming.com/en",
    "ZIM": "https://www.zimshipping.com.cn/za-hant/tools/track-a-shipment"
}

export function getTrackingUrl(carrierCode: string, cntrNo: string): string | null {
    const baseUrl = tracking_urls[carrierCode];

    if (!baseUrl) {
        return null;
    }

    // 需要在URL中直接拼接容器号的承运商
    const carriersWithDirectAppend = ["HPL", "PIL"];

    if (carriersWithDirectAppend.includes(carrierCode)) {
        return baseUrl + cntrNo;
    }

    return baseUrl;
}