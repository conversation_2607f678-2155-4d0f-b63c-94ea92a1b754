/** 请求服务的环境配置 */
type ServiceEnv = Record<ServiceEnvType, ServiceEnvConfig>;

/** 不同请求服务的环境配置 */
const serviceEnv: ServiceEnv = {
	dev: {
		url: "http://localhost:8081",
		proxyPattern: "/gaia/api/a",
		casPattern: "/gaia/api/cas",
		casUrl: "http://cas2-dev.fuyouzhao.com",
	},
	test: {
		url: "https://bestmart360-test.fuyouzhao.com/",
		proxyPattern: "/api",
		casPattern: "/gaia/api/cas",
		casUrl: "http://smart-track-test.arm-hoitung.com",
	},
	uat: {
		url: "http://yyds-uat.fuyouzhao.com",
		proxyPattern: "/gaia/api/a",
		casPattern: "/gaia/api/cas",
		casUrl: "http://cas2-uat.fuyouzhao.com",
	},
	prod: {
		url: "https://yyds.fuyouzhao.com",
		proxyPattern: "/gaia/api/a",
		casPattern: "/gaia/api/cas",
		casUrl: "https://cas2.fuyouzhao.com",
	},
};

/**
 * 获取当前环境模式下的请求服务的配置
 * @param env 环境
 */
export function getServiceEnvConfig(
	env: ImportMetaEnv
): ServiceEnvConfigWithProxyPattern {
	const { VITE_SERVICE_ENV = "dev" } = env;

	const config = serviceEnv[VITE_SERVICE_ENV];

	return {
		...config,
	};
}
