module.exports = {
	extends: [
		"eslint:recommended",
		"plugin:vue/vue3-essential",
		"plugin:@typescript-eslint/recommended",
	],
	overrides: [
		{
			files: ["./scripts/*.ts"],
			rules: {
				"no-unused-expressions": "off",
			},
		},
		{
			files: ["*.vue"],
			rules: {
				"no-undef": "off", // use tsc to check the ts code of the vue
			},
		},
	],
	settings: {
		"import/core-modules": [
			"uno.css",
			"~icons/*",
			"virtual:svg-icons-register",
		],
	},
	rules: {
		"import/order": [
			"warning",
			{
				"newlines-between": "never",
				groups: [
					"builtin",
					"external",
					"internal",
					"parent",
					"sibling",
					"index",
				],
				pathGroups: [
					{
						pattern: "vue",
						group: "external",
						position: "before",
					},
					{
						pattern: "vue-router",
						group: "external",
						position: "before",
					},
					{
						pattern: "pinia",
						group: "external",
						position: "before",
					},
					{
						pattern: "naive-ui",
						group: "external",
						position: "before",
					},
					{
						pattern: "@/constants",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/config",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/settings",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/plugins",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/layouts",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/views",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/components",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/router",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/service",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/store",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/context",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/composables",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/hooks",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/utils",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/assets",
						group: "internal",
						position: "before",
					},
					{
						pattern: "@/**",
						group: "internal",
						position: "before",
					},
				],
				pathGroupsExcludedImportTypes: [
					"vue",
					"vue-router",
					"pinia",
					"naive-ui",
				],
			},
		],
	},
};
