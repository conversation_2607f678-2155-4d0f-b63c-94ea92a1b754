import { getServiceEnvConfig } from "~/.env-config";
import { createRequest } from "./request";

const { url, proxyPattern, casPattern, casUrl } = getServiceEnvConfig(
	import.meta.env
);

const isHttpProxy = import.meta.env.VITE_HTTP_PROXY === "Y";
console.log('pppppppp-----------',isHttpProxy)
export const request = createRequest({
	baseURL: isHttpProxy ? url : url + proxyPattern,
	// withCredentials: true,
});
export const casRequest = createRequest({
	baseURL: isHttpProxy ? casPattern : casUrl + casPattern,
	// withCredentials: true,
});

export const mockRequest = createRequest({ baseURL: "/mock" });
export const devRequest = createRequest({ baseURL: "/" });
