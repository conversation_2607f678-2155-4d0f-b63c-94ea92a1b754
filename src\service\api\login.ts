import { casRequest } from "../request";

/** 登录 */
export const fetchLogin = async (params: any) => {
	const data = await casRequest.post("/admin-login/", params);
	return data;
};
/** 校验登录token */
export const fetchVerificationToken = async (params: any) => {
	const data = await casRequest.post("/admin-login/verification-token", params);
	return data;
};
/** 获取图形验证码 */
export const fetchImgCaptcha = async (params: any) => {
	const data = await casRequest.post(`/admin-login/getCaptcha?accountType=${params.accountType}`, params);
	return data;
};


