/**
 * 根据数字获取对应的汉字
 * @param num - 数字(0-10)
 */
export function getHanByNumber(num: number) {
	const HAN_STR = "零一二三四五六七八九十";
	return HAN_STR.charAt(num);
}

/**
 * 将总秒数转换成 分：秒
 * @param seconds - 秒
 */
export function transformToTimeCountDown(seconds: number) {
	const SECONDS_A_MINUTE = 60;
	function fillZero(num: number) {
		return num.toString().padStart(2, "0");
	}
	const minuteNum = Math.floor(seconds / SECONDS_A_MINUTE);
	const minute = fillZero(minuteNum);
	const second = fillZero(seconds - minuteNum * SECONDS_A_MINUTE);
	return `${minute}: ${second}`;
}

/**
 * 获取指定整数范围内的随机整数
 * @param start - 开始范围
 * @param end - 结束范围
 */
export function getRandomInteger(end: number, start = 0) {
	const range = end - start;
	const random = Math.floor(Math.random() * range + start);
	return random;
}

/**
 * 四舍五入的精确方法
 * @param number{number} 基础数值
 * @param toFixed{number} 保留小数位数，不能为负数，0为整数
 * @return {number}
 */
export function MathToFixed(number: number, toFixed: number = 2) {
	return Number(Number(number).toFixed(toFixed)) 
}

export function formatNumber(value: string): string {
	if(!value) return '0.00'
	// 将字符串转换为浮点数

	const numberValue = Number.parseFloat(value)
	// 判断是否为数字类型
	if (Number.isNaN(numberValue)) {
	  throw new Error('Invalid number format.')

	}
  
	// 保留两位小数并进行四舍五入
	const roundedValue = numberValue.toFixed(2)
  
	// 将浮点数转换为千分位表示
	const result = Number.parseFloat(roundedValue).toLocaleString(undefined, {
	  minimumFractionDigits: 2,
	  maximumFractionDigits: 2,
	});
  
	return result;
  }

  export function MathToFixedStr(num: any, precision: any, tag: any){
	if (typeof num === 'number' && !isNaN(num)) {
		// value是非NaN的数字
		return num.toFixed(precision)
	}else{
		return 0
	}
  }